import { createRouter, createWebHashHistory } from 'vue-router';
import HomeView from '../layout/HomeView.vue';
import store from '@/store';
import * as Tools from '@/utils/tool.js';
import { menuData } from './menu.js';

const constantRoutes = [
  { path: '/Login', name: 'Login', meta: { title: '登录' }, component: () => import('../views/LoginView.vue') },
  // { path: '/Login', name: 'Login', meta: { title: '登录' }, component: LoginView },
  { path: '/', name: 'Home', component: HomeView, redirect: '/index', children: menuData },
  { path: '/:pathMatch(.*)', component: () => import('../views/404.vue') }
  // {
  //   path: '/',
  //   name: 'Home',
  //   component: HomeView,
  //   redirect: '/index',
  //   children: [
  //     {
  //       name: 'index',
  //       path: '/index',
  //       component: IndexView,
  //       meta: {
  //         title: '首页',
  //         keepAlive: 1,
  //         icon: ['fas', 'house']
  //       }
  //     }
  //   ]
  // }
];

const asyncRoutes = {
  path: '/',
  component: HomeView,
  redirect: '/IndexView', // 默认页面
  children: [
    // {
    //   path: '/KttMainDetail/:id',
    //   name: 'KttMainDetail',
    //   meta: { title: '快团团订单详情', keepAlive: true },
    //   component: () => import('../views/ktt/KttMainDetail.vue')
    // }
  ]
};

// 配置常量菜单
export const constantMenu = [
  {
    id: 1000,
    name: '首页',
    url: 'IndexView',
    openStyle: 0,
    icon: 'fas:home-lg'
  }
];

// 白名单列表
const whiteList = ['/Login'];

// export const errorRoute = {
//   path: '/:pathMatch(.*)',
//   redirect: '/404'
// };

const router = createRouter({
  history: createWebHashHistory(),
  routes: constantRoutes
});

store.commit('router/setRoutes', menuData);
store.commit('router/setMenuRoutes', menuData);

router.beforeEach(async (to, from, next) => {
  if (store.state.app.authToken) {
    if (to.path === '/Login') {
      // 登录成功后，跳转页面
      next('/');
    } else {
      next();
    }
    if (to.meta.keepAlive) {
      store.commit('tags/ADD_VIEW', {
        keepAlive: to.meta.keepAlive,
        title: to.meta.title,
        icon: to.meta.icon,
        path: to.path,
        name: to.name
      });
    }
  } else {
    // 没有token的情况下，可以进入白名单
    if (whiteList.indexOf(to.path) > -1) {
      next();
    } else {
      next('/Login');
    }
  }
});

export default router;

// 获取扁平化路由，将多级路由转换成一级路由
export const getKeepAliveRoutes = (rs, breadcrumb) => {
  const routerList = [];
  rs.forEach((item) => {
    if (item.meta.title) {
      // breadcrumb.push(item.meta.title);
    }

    if (item.children && item.children.length > 0) {
      routerList.push(...getKeepAliveRoutes(item.children, breadcrumb));
    } else {
      item.meta.breadcrumb.push(...breadcrumb);
      routerList.push(item);
    }

    // breadcrumb.pop();
  });
  return routerList;
};

export const layoutModules = () => {
  return import.meta.glob('/src/views/**/*.vue');
};
// 根据路径，动态获取vue组件
const getDynamicComponent = (path) => {
  const modules = layoutModules();
  return modules[`/src/views/${path}.vue`];
};

// 根据菜单列表，生成路由数据
export const generateRoutes = (menuList) => {
  const routerList = [];

  menuList.forEach((menu) => {
    let component;
    let path;
    if (menu.children && menu.children.length > 0) {
      component = HomeView;
      path = '/p/' + menu.id;
    } else {
      // 判断是否iframe
      if (isIframeUrl(menu)) {
        component = () => import('@/layout/components/Router/Iframe.vue');
        path = '/iframe/' + menu.id;
      } else {
        component = getDynamicComponent(menu.url);
        path = '/' + menu.url;
      }
    }

    let name = menu.url;
    if (name) {
      let _i = name.lastIndexOf('/');
      if (_i >= 0 && _i < name.length) {
        name = name.substring(_i + 1) || menu.url;
      }
    } else {
      name = Tools.pathToCamel(path);
    }
    const route = {
      path: path,
      name: name,
      // name: Tools.pathToCamel(path),
      component: component,
      children: [],
      meta: {
        title: menu.name,
        icon: menu.icon,
        id: '' + menu.id,
        url: menu.url,
        keepAlive: true,
        newOpen: menu.openStyle === 1,
        affix: menu.affix,
        breadcrumb: []
      }
    };

    // 有子菜单的情况
    if (menu.children && menu.children.length > 0) {
      route.children?.push(...generateRoutes(menu.children));
    }

    routerList.push(route);
  });

  return routerList;
};

// 判断是否iframe
const isIframeUrl = (menu) => {
  // 如果是新页面打开，则不用iframe
  if (menu.openStyle === 1) {
    return false;
  }

  // 是否外部链接
  return Tools.isExternalLink(menu.url);
};
