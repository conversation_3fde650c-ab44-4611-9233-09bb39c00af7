<template>
  <div>
    <el-dialog v-model="visible" title="发货/线下服务" width="600px" :close-on-click-modal="false" draggable>
      <el-form ref="inputForm" :model="inputForm" v-loading="loading" label-width="180px">

        <el-row :gutter="15" style="margin-top: 30px;">
          <el-col :span="24">
            <el-form-item label="是否需要快递配送" prop="status">
              <el-radio-group v-model="inputForm.status">
                <el-radio :value="2">发快递</el-radio>
                <el-radio :value="3">线下服务</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
          <el-col :span="24" v-if="inputForm.status===2">
            <el-form-item label="快递单号" prop="deliveryCode">
              <el-input v-model="inputForm.deliveryCode" placeholder="请输入快递单号" style="width: 340px"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="24" v-if="inputForm.status===2">
            <el-form-item label="快递公司" prop="shipper">
              <el-select v-model="inputForm.shipper" placeholder="请选择快递公司" style="width: 340px">
                <el-option v-for="item in options" :key="item" :label="item" :value="item" />
              </el-select>
            </el-form-item>
          </el-col>

        </el-row>

      </el-form>

      <template #footer>
        <span class="dialog-footer">
          <el-button size="large" @click="visible = false">关闭</el-button>
          <el-button size="large" type="success" :loading="loading" @click="doSubmit()">提交</el-button>
        </span>
      </template>

    </el-dialog>
  </div>
</template>

<script>
import OrderApi from '@/api/OrderApi';
import { mapGetters } from 'vuex';

export default {
  props: {},
  data() {
    return {
      options: [
        '圆通快递',
        '申通快递',
        '韵达快递',
        '顺丰快递',
        '中通快递',
        '天天快递',
        '京东快递',
        '极兔快递',
        '百世快递',
        'EMS',
        '德邦',
        '跨越'
      ],
      activeName: 0,
      title: '',
      nonull: { required: true, message: '必填' },
      visible: false,
      loading: false,
      details: {},
      inputForm: {
        orderCode: '',
        shipper: '',
        status: '',
        deliveryCode: ''
      }
    };
  },
  components: {},
  computed: {
    ...mapGetters('app', [])
  },
  created() {},
  mounted() {},
  methods: {
    doSubmit() {
      this.$refs['inputForm'].validate((valid) => {
        if (valid) {
          this.loading = true;
          OrderApi.sendDelivery(this.inputForm)
            .then(() => {
              this.visible = false;
              this.$message.success('操作成功');
              this.$emit('refreshDataList');
              this.loading = false;
            })
            .catch(() => {
              this.loading = false;
            });
        }
      });
    },
    init(orderCode) {
      this.inputForm.orderCode = orderCode;
      this.visible = true;
      this.loading = false;
      this.$nextTick(() => {
        this.$refs.inputForm.resetFields();
        this.loading = true;
        OrderApi.orderInfo(this.inputForm.orderCode).then((data) => {
          this.details = data;
          this.inputForm.orderCode = data.orderCode;
          this.loading = false;
        });
      });
    }
  }
};
</script>

<style lang="scss" scoped>
.info-value {
  font-size: 16px;
  font-weight: bold;
  color: #333;
}

.info-img {
  border-radius: 6pxs;
  border: 1px solid #ddd;
  width: 150px;
  height: 150px;
  margin-right: 20px;
}
</style>