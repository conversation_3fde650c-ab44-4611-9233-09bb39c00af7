<template>
  <div class="navbar">

    <div class="right-menu">
      <el-dropdown class="right-menu-item hover-effect" trigger="click">
        <div class="avatar-wrapper">
          <div class="user-name">您好，{{ loginUserName }}</div>
          <img src="@/assets/images/avatar.png" class="user-avatar">
        </div>

        <template #dropdown>
          <el-dropdown-menu>
            <!-- <el-dropdown-item>个人中心</el-dropdown-item> -->
            <!-- <el-dropdown-item>修改密码</el-dropdown-item> -->
            <el-dropdown-item divided @click="open">退出登录</el-dropdown-item>
          </el-dropdown-menu>
        </template>

      </el-dropdown>
    </div>
  </div>
</template>

<script>
import { mapGetters } from 'vuex';

export default {
  name: 'NavBar',
  components: {},
  data() {
    return {};
  },
  computed: {
    ...mapGetters('app', ['loginUserName'])
  },
  methods: {
    open() {
      this.$confirm('确定注销并退出系统吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          this.logout();
        })
        .catch(() => {});
    },
    logout() {
      this.$store.commit('app/logout');
      this.$router.replace('/Login');
    }
  }
};
</script>

<style lang="scss" scoped>
.navbar {
  height: 50px;
  overflow: hidden;
  position: relative;
  background: #fff;
  box-shadow: 0 1px 4px rgba(0, 21, 41, 0.08);

  .right-menu {
    float: right;
    height: 100%;
    line-height: 50px;

    &:focus {
      outline: none;
    }

    .right-menu-item {
      margin-right: 30px;
      display: inline-block;
      padding: 0 8px;
      height: 100%;
      color: #5a5e66;

      &.hover-effect {
        cursor: pointer;

        &:hover {
          background: rgba(0, 0, 0, 0.025);
        }
      }
    }

    .avatar-wrapper {
      height: 100%;
      position: relative;
      display: flex;
      flex-direction: row;
      align-items: center;

      .user-avatar {
        display: inline-block;
        cursor: pointer;
        width: 40px;
        height: 40px;
        border-radius: 10px;
      }

      .user-name {
        display: inline-block;
        margin-right: 10px;
        font-size: 15px;
      }
    }
  }
}
</style>
