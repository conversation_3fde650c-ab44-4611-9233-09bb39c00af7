import { generateRoutes, constantMenu } from '@/router';

const state = {
  isFinish: false, // 菜单数据是否加载完成
  menuRoutes: [], // 菜单数据
  routes: [] //  路由数据
};

const mutations = {
  setMenuRoutes(state, menuRoutes) {
    state.menuRoutes = menuRoutes;
  },
  pushMenuRoutes(state, menuRoutes) {
    state.menuRoutes.push(...menuRoutes);
  },
  setRoutes(state, routers) {
    this.routes = routers;
  },
  setIsFinish(state, isFinish) {
    state.isFinish = isFinish;
  }
};

const getters = {
  menuRoutes: (state) => state.menuRoutes,
  isFinish: (state) => state.isFinish
};
const actions = {
};

export default {
  namespaced: true,
  state,
  mutations,
  getters,
  actions
};
