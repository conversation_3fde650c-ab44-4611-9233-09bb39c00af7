const state = {
  user: {}, // 当前登录用户
  authorityList: [], // 权限列表
  authToken: ''
};

const mutations = {
  setUser(state, user) {
    state.user = user;
  },
  setAuthToken(state, authToken) {
    state.authToken = authToken;
  },
  setAuthorityList(state, authorityList) {
    state.authorityList = authorityList;
  },
  logout(state) {
    state.authToken = '';
    state.user = {};
    state.authorityList = [];
  }
};

const getters = {
  loginUserName: (state) => (state.user ? state.user.name : '管理员'),
  loginUserId: (state) => state.user.id,
  loginUserCode: (state) => state.user.code,
  authorityList: (state) => state.authorityList,
  authToken: (state) => state.authToken
};

export default {
  namespaced: true,
  state,
  mutations,
  getters
};
