import axios from 'axios';
// import qs from 'qs';
import router from '@/router';
import Config from '@/settings';
import store from '@/store';
import { ElMessage } from 'element-plus';

// 创建axios实例
const _axios = axios.create({
  // baseURL: '/antique',
  timeout: Config.timeout // 请求超时时间
});

if (import.meta.env.PROD) {
  _axios.defaults.baseURL = import.meta.env.VITE_API_URL;
}

// request拦截器
_axios.interceptors.request.use(
  (config) => {
    config.headers['Content-Type'] = 'application/json; charset=utf-8';
    if (store.state.app.authToken) {
      config.headers['Authorization'] = 'Bearer ' + store.state.app.authToken;
    }
    // if (config.method === 'post' && config.headers['Content-Type'].indexOf('multipart/form-data') === -1) {
    //   config.data = qs.stringify(config.data, { arrayFormat: 'repeat' });
    // }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// response 拦截器
_axios.interceptors.response.use(
  (response) => {
    const res = response.data;
    if (response.status && response.status === 200) {
      return res;
    }

    if (response.status !== 0) {
      if (response.status === 401) {
        router.replace('/Login');
      }
      return Promise.reject(new Error(res.msg || 'Error'));
    }
    return res;
  },
  (error) => {
    if (!error.response) {
      ElMessage.error('无法连接服务器');
      return Promise.reject(error);
    } else {
      if (error.response.status === 403) {
        ElMessage.error('登录已失效,请重新登陆');
      } else if (error.response.status === 500) {
        console.log(error.response);
        if (error.response.data && error.response.data.msg) {
          ElMessage.error(error.response.data.msg);
        } else {
          ElMessage.error('接口请求失败');
        }
      }
    }
    return Promise.reject(error);
  }
);
export default _axios;
