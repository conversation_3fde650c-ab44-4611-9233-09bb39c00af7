<template>
  <div class="app-container">
    <el-form ref="searchForm" :model="searchForm" label-width="80px">
      <el-row>
        <el-col :span="6">
          <el-form-item label="订单号" prop="orderCode">
            <el-input v-model="searchForm.orderCode" style="width:200px;" clearable />
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="开票状态" prop="status">
            <el-select v-model="searchForm.status" placeholder="请选择开票状态">
              <el-option label="待开" value="0"></el-option>
              <el-option label="已开" value="1"></el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item>
            <el-button type="success" icon="search" @click="search">查询</el-button>
            <el-button type="warning" icon="refresh-left" @click="reset">重置</el-button>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>

    <el-table :data="tableData" border style="margin-top: 10px; " v-loading="loading">
      <el-table-column type="index" label="#" width="55" align="center" />
      <el-table-column prop="orderCode" label="订单号" width="230">
        <template #default="scope">
          <el-link type="primary" underline="never" @click="toDetail(scope.row)">{{ scope.row.orderCode }}</el-link>
        </template>
      </el-table-column>
      <el-table-column prop="amount" label="开票金额" width="110">
        <template #default="scope">
          ￥ {{ scope.row.amount.toFixed(2) }}
        </template>
      </el-table-column>
      <el-table-column prop="titleName" label="发票抬头" width="160"></el-table-column>
      <el-table-column prop="taxId" label="纳税人识别号" width="160"></el-table-column>
      <!-- <el-table-column prop="titleType" label="抬头类型" width="130"></el-table-column> -->

      <el-table-column prop="invoiceType" label="发票类型" width="190">
        <template #default="scope">
          <span v-if="scope.row.invoiceType === 1">增值税普通发票（普票）</span>
          <span v-else-if="scope.row.invoiceType === 2">增值税专用发票（专票）</span>

        </template>
      </el-table-column>
      <el-table-column prop="status" label="开票状态" width="130">
        <template #default="scope">
          <el-tag v-if="scope.row.status === 0" type="warning">待开</el-tag>
          <el-tag v-else-if="scope.row.status === 1" type="success">已开</el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="createTime" label="申请时间" width="140">
        <template #default="scope">
          {{ scope.row.createTime.substring(0, 16) }}
        </template>
      </el-table-column>
      <el-table-column label="操作">
        <template #default="scope">
          <el-button v-if="scope.row.status === 0" type="primary" link @click="toEdit(scope.row)">开票上传</el-button>
          <el-button v-if="scope.row.status === 1" type="primary" link @click="toEdit(scope.row)">查看</el-button>
        </template>
      </el-table-column>

    </el-table>
    <el-pagination style="margin-top: 10px;" ref="pagination" :total="pagination.total" v-model:current-page="pagination.current"
      v-model:page-size="pagination.size" :page-sizes="[10, 20, 50]" @size-change="handlePageSizeChange" @current-change="handlePageCurrentChange"
      layout="prev, pager, next, total, sizes">
    </el-pagination>

    <OrderDetails ref="orderDetails" @refreshDataList="refreshList"></OrderDetails>
    <OrderInvoiceDetails ref="orderInvoiceDetails" @refreshDataList="refreshList"></OrderInvoiceDetails>
  </div>
</template>

<script>
import OrderDetails from '@/views/order/OrderDetails.vue';
import Config from '@/settings';
import OrderInvoiceApi from '@/api/OrderInvoiceApi';
import OrderInvoiceDetails from './OrderInvoiceDetails.vue';

export default {
  name: 'OrderInvoiceList',
  data() {
    return {
      tableData: [],
      searchForm: {
        orderCode: '',
        status: ''
      },
      pagination: {
        total: 0,
        size: Config.pageSize,
        current: 1
      },
      loading: false
    };
  },
  components: { OrderDetails, OrderInvoiceDetails },
  activated() {
    this.refreshList();
  },
  methods: {
    toDetail(row) {
      this.$refs.orderDetails.init(row.orderCode);
    },
    toEdit(row) {
      this.$refs.orderInvoiceDetails.init(row.id);
    },
    search() {
      this.pagination.current = 1;
      this.refreshList();
    },
    reset() {
      this.$refs.searchForm.resetFields();
      this.pagination.current = 1;
      this.refreshList();
    },
    refreshList() {
      this.loading = true;
      OrderInvoiceApi.orderInvoiceList(this.pagination, this.searchForm).then((data) => {
        this.tableData = data.records;
        this.pagination.total = data.total;
        // 删除后如果当前页没数据,则返回前一页
        if (data.records.length === 0 && data.total > 0 && this.pagination.current > 1) {
          this.pagination.current = this.pagination.current - 1;
          this.refreshList();
        }
        this.loading = false;
      });
    },
    handlePageSizeChange() {
      this.pagination.current = 1;
      this.refreshList();
    },
    handlePageCurrentChange() {
      this.refreshList();
    }
  }
};
</script>

<style lang="scss" scoped>
</style>