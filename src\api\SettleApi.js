import request from '@/utils/request';

export default {
  settleList(pagination, searchForm) {
    return request({
      url: '/merchant/settle/list',
      method: 'post',
      data: {
        ...pagination,
        ...searchForm
      }
    });
  },

  settleInfo(settleId) {
    return request({
      url: '/merchant/settle/info',
      method: 'post',
      data: {
        settleId
      }
    });
  },
  settleRecordsList(pagination, searchForm) {
    return request({
      url: '/merchant/settleRecords/list',
      method: 'post',
      data: {
        ...pagination,
        ...searchForm
      }
    });
  }
};
