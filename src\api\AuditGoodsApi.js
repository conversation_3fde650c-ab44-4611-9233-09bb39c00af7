import request from '@/utils/request';

export default {
  auditGoodsList(pagination, searchForm) {
    return request({
      url: '/merchant/auditGoods/list',
      method: 'post',
      data: {
        ...pagination,
        ...searchForm
      }
    });
  },

  auditGoodsInfo(id) {
    return request({
      url: '/merchant/auditGoods/info',
      method: 'post',
      data: {
        id
      }
    });
  },

  auditGoodsDelete(id) {
    return request({
      url: '/merchant/auditGoods/delete',
      method: 'post',
      data: {
        id
      }
    });
  },

  auditGoodsSave(data) {
    if (!data.id) {
      return request({
        url: '/merchant/auditGoods/add',
        method: 'post',
        data: {
          ...data
        }
      });
    } else {
      return request({
        url: '/merchant/auditGoods/update',
        method: 'post',
        data: {
          ...data
        }
      });
    }
  }
};
