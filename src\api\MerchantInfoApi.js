import request from '@/utils/request';

export default {
  merchantInfo(id) {
    return request({
      url: '/merchant/merchantInfo/myInfo',
      method: 'post',
      data: {
        id
      }
    });
  },
  merchantInfoUpdateSettle(params) {
    return request({
      url: '/merchant/merchantInfo/updateSettle',
      method: 'post',
      data: {
        ...params
      }
    });
  },

  merchantApply(data) {
    return request({
      url: '/merchant/merchantInfo/apply',
      method: 'post',
      data: {
        ...data
      }
    });
  },

  merchantSearchAudit(auditId) {
    return request({
      url: '/merchant/merchantInfo/searchAudit',
      method: 'post',
      data: {
        auditId
      }
    });
  }
};
