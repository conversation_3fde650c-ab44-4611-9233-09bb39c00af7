import request from '@/utils/request';

export default {
  login(userCode, userPwd, verifyCode, verifyUuid) {
    return request({
      url: '/merchant/auth/login',
      method: 'post',
      data: {
        userCode,
        userPwd,
        verifyCode,
        verifyUuid
      }
    });
  },

  getInfo() {
    return request({
      url: '/merchant/auth/info',
      method: 'get'
    });
  },

  getVerifyCode() {
    return request({
      url: '/merchant/auth/verifyCode',
      method: 'get'
    });
  },

  logout() {
    return request({
      url: '/merchant/auth/logout',
      method: 'delete'
    });
  }
};
