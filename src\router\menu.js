// import IndexView from '../views/IndexView.vue';

export const menuData = [
  {
    name: 'index',
    path: '/index',
    component: () => import('../views/IndexView.vue'),
    meta: { title: '首页', keepAlive: true, icon: 'fas:home-lg' }
  },
  {
    name: 'goods',
    path: '/goods',
    meta: { title: '商品', keepAlive: true, icon: 'fas:box' },
    children: [
      {
        name: 'GoodsList',
        path: '/goods/goodsList',
        component: () => import('../views/goods/goods/GoodsList.vue'),
        meta: { title: '我的商品', keepAlive: true, icon: '' }
      },
      {
        name: 'AuditGoodsList',
        path: '/auditGoods/auditGoodsList',
        component: () => import('../views/goods/auditGoods/AuditGoodsList.vue'),
        meta: { title: '商品审核记录', keepAlive: true, icon: '' }
      },
      {
        name: 'GoodsSpecList',
        path: '/goods/goodsSpec',
        component: () => import('../views/goods/spec/GoodsSpecList.vue'),
        meta: { title: '规格管理', keepAlive: true, icon: '' }
      }
    ]
  },
  {
    name: 'order',
    path: '/order',
    meta: { title: '订单', keepAlive: true, icon: 'fas:paste' },
    children: [
      {
        name: 'OrderList',
        path: '/order/orderList',
        component: () => import('../views/order/OrderList.vue'),
        meta: { title: '我的订单', keepAlive: true, icon: '' }
      },
      {
        name: 'OrderInvoiceList',
        path: '/orderInvoice/orderInvoiceList',
        component: () => import('../views/invoice/OrderInvoiceList.vue'),
        meta: { title: '开发票', keepAlive: true, icon: '' }
      }
    ]
  },
  {
    name: 'customer',
    path: '/customer',
    meta: { title: '客户', keepAlive: true, icon: 'fas:user' },
    children: [
      {
        name: 'CustomerList',
        path: '/customer/CustomerList',
        component: () => import('../views/customer/CustomerList.vue'),
        meta: { title: '我的客户', keepAlive: true, icon: '' }
      },
      {
        name: 'CustomerPotentialList',
        path: '/customer/customerPotentialList',
        component: () => import('../views/customer/CustomerPotentialList.vue'),
        meta: { title: '潜在客户', keepAlive: true, icon: '' }
      }
    ]
  },
  {
    name: 'financial',
    path: '/financial',
    meta: { title: '财务中心', keepAlive: true, icon: 'fas:cash-register' },
    children: [
      {
        name: 'FinancialInFlowList',
        path: '/financial/FinancialInFlowList',
        component: () => import('../views/financial/FinancialInFlowList.vue'),
        meta: { title: '收款记录', keepAlive: true, icon: '' }
      },
      {
        name: 'FinancialOutFlowList',
        path: '/financial/FinancialOutFlowList',
        component: () => import('../views/financial/FinancialOutFlowList.vue'),
        meta: { title: '退款流水', keepAlive: true, icon: '' }
      },
      {
        name: 'SettleList',
        path: '/settle/SettleList',
        component: () => import('../views/settle/SettleList.vue'),
        meta: { title: '结算账单', keepAlive: true, icon: '' }
      }
    ]
  },
  // {
  //   name: 'statistics',
  //   path: '/statistics',
  //   meta: { title: '数据统计', keepAlive: true, icon: 'fas:home-lg' },
  //   children: [
  //     {
  //       name: 'customerStatistics',
  //       path: '/statistics/customerStatistics',
  //       component: () => import('../views/IndexView.vue'),
  //       meta: { title: '会员统计', keepAlive: true, icon: 'fas:home-lg' }
  //     },
  //     {
  //       name: 'orderStatistics',
  //       path: '/statistics/orderStatistics',
  //       component: () => import('../views/IndexView.vue'),
  //       meta: { title: '订单统计', keepAlive: true, icon: 'fas:home-lg' }
  //     },
  //     {
  //       name: 'goodsStatistics',
  //       path: '/statistics/goodsStatistics',
  //       component: () => import('../views/IndexView.vue'),
  //       meta: { title: '商品统计', keepAlive: true, icon: 'fas:home-lg' }
  //     },
  //     {
  //       name: 'trafficStatistics',
  //       path: '/statistics/trafficStatistics',
  //       component: () => import('../views/IndexView.vue'),
  //       meta: { title: '流量统计', keepAlive: true, icon: 'fas:home-lg' }
  //     }
  //   ]
  // }
  {
    name: 'settings',
    path: '/settings',
    meta: { title: '设置', keepAlive: true, icon: 'fas:cog' },
    children: [
      {
        name: 'myInfo',
        path: '/settings/MyInfo',
        component: () => import('../views/settings/MyInfoView.vue'),
        meta: { title: '我的信息', keepAlive: true, icon: '' }
      },
      {
        name: 'settleAccounts',
        path: '/settings/SettleAccounts',
        component: () => import('../views/settings/SettleView.vue'),
        meta: { title: '结算信息', keepAlive: true, icon: '' }
      },
      {
        name: 'updatePassword',
        path: '/settings/UpdatePassword',
        component: () => import('../views/settings/UpdatePasswordView.vue'),
        meta: { title: '修改密码', keepAlive: true, icon: '' }
      }
    ]
  }
];
