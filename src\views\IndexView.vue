<template>
  <div class="app-container" style="background-color: #F9F9F9;">

    <div class="card">
      <h4>待办事项</h4>

      <div class="detail-list">
        <div class="detail-item" @click="toOrderList">
          <div>
            <span>{{ homeData.unPaidOrder || 0 }}</span>
            <div>待付款</div>
          </div>
          <div class="detail-title">
            交易前
          </div>
        </div>

        <div class="detail-item" @click="toOrderList">
          <div>
            <span>{{ homeData.unDeliveredOrder || 0 }}</span>
            <div>待发货</div>
          </div>
          <div>
            <span>{{ homeData.deliveredOrder || 0 }}</span>
            <div>待收货</div>
          </div>
          <div class="detail-title">
            交易中
          </div>
        </div>
        <div class="detail-item">
          <div @click="toOrderList">
            <span>{{ homeData.returnMoney || 0 }}</span>
            <div>退款</div>
          </div>
          <!-- <div @click="navigateTo('returnGoodsOrder')">
            <span>{{ homeData.returnGoods || 0 }}</span>
            <div>退货</div>
          </div>
          <div @click="navigateTo('memberComment')">
            <span>{{ homeData.memberEvaluation || 0 }}</span>
            <div>待评价</div>
          </div> -->
          <div class="detail-title">
            交易后
          </div>
        </div>
        <!-- <div class="detail-item" @click="navigateTo('orderComplaint')">
          <div>
            <span>{{ homeData.complaint || 0 }}</span>
            <div>待处理</div>
          </div>

          <div class="detail-title">
            投诉
          </div>
        </div> -->
        <div class="detail-item" @click="toAuditGoodsList">
          <!-- <div @click="navigateTo('alert-goods-quantity')">
            <span>{{ homeData.alertQuantityNum || 0 }}</span>
            <div>库存预警</div>
          </div> -->
          <div>
            <span>{{ homeData.waitAuth || 0 }}</span>
            <div>审核中</div>
          </div>
          <div class="detail-title">
            商品
          </div>
        </div>

        <div class="detail-item">
          <!-- <div @click="navigateTo('seckill')">
            <span>{{ homeData.seckillNum || 0 }}</span>
            <div>秒杀活动</div>
          </div> -->
          <div>
            <span>{{ homeData.waitPayBill || 0 }}</span>
            <div>等待对账</div>
          </div>
          <div class="detail-title">
            其他
          </div>
        </div>

      </div>
    </div>

    <div class="card">
      <h4>统计数据</h4>
      <div class="count-list" style="display: flex;">
        <div class="count-item" @click="navigateTo('goods')">
          <div class="counts">{{ homeData.goodsNum || 0 }}</div>
          <div>上架商品数量</div>

        </div>
        <div class="count-item" @click="navigateTo('orderStatistics')">
          <div class="counts">{{ homeData.walletPrice || 0  }}</div>
          <div>账户余额</div>

        </div>
        <!-- <div class="count-item" @click="navigateTo('orderStatistics')">
          <div>
            <Icon class="icon" size="31" type="ios-card" />
          </div>
          <div class="counts">{{ homeData.orderPrice || 0  }}</div>
          <div>今日订单总额</div>

        </div> -->
        <div class="count-item" @click="navigateTo('orderList')">
          <div class="counts">{{ homeData.orderNum || 0 }}</div>
          <div>今日订单数量</div>

        </div>
        <!-- <div class="count-item" @click="navigateTo('trafficStatistics')">
          <div>
            <Icon class="icon" size="31" type="md-person" />
          </div>
          <div class="counts">{{ homeData.storeUV || 0 }}</div>
          <div>今日访客数量</div>

        </div> -->
      </div>
    </div>

  </div>
</template>

<script>
import HomeApi from '@/api/HomeApi';

export default {
  name: 'index',
  data() {
    return {
      homeData: {
        orderPrice: 10
      }
    };
  },
  components: {},
  activated() {
    HomeApi.home().then((data) => {
      this.homeData = data;
    });
  },
  methods: {
    toAuditGoodsList() {
      this.$router.push({ path: '/auditGoods/auditGoodsList' });
    },
    toOrderList() {
      this.$router.push({ path: '/order/orderList' });
    }
  }
};
</script>

<style scoped lang="scss">
.card {
  margin: 10px 10px 20px;
  padding: 0 20px 20px;
  background: #fff;
  border: 1px solid #e7e7e7;
  border-radius: 0.4em;
  box-shadow: 0px 0px 8px rgba($color: #999999, $alpha: 0.3);
}

.detail-list {
  display: flex;
  flex-wrap: wrap;

  .detail-title {
    position: absolute;
    left: 0;
    top: -15px;
    opacity: 0.3;
    color: #999;
    font-size: 21px;
    text-decoration: initial;
    transition: 0.35s;
  }

  .detail-item {
    cursor: pointer;
    transition: 0.35s;
    position: relative;
    font-weight: bold;
    width: 286px;
    display: flex;
    span {
      color: #ff9a76;
      font-size: 18px;
    }
    align-items: center;
    justify-content: center;
    padding: 20px;
    background: #eee;
    border-radius: 0.4em;
    margin: 10px;
    > div {
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      margin: 0 20px;
    }
  }
  .detail-item:hover {
    box-shadow: 3px 5px 12px rgba(0, 0, 0, 0.2);
    transform: translateY(-4px);
    > .detail-title {
      opacity: 1;

      top: 5px;
    }
  }
}

.count-item,
.todo-item {
  height: 84px;
  display: flex;
  flex-direction: column;
  cursor: pointer;
  justify-content: center;
  align-items: center;
  color: #fff;
  border-radius: 0.4em;
  flex: 1;
  font-weight: bold;
  margin-right: 20px;
}

.count-item:nth-of-type(1) {
  background-image: linear-gradient(109.6deg, rgba($color: #ff9a76, $alpha: 0.6) 11.2%, #ff9a76 100.2%);
  box-shadow: 1px 3px 12px rgba($color: #ff9a76, $alpha: 0.3);
}
.count-item:nth-of-type(2) {
  background-image: linear-gradient(109.6deg, rgba($color: #4e89ae, $alpha: 0.6) 11.2%, #4e89ae 100.2%);
  box-shadow: 1px 3px 12px rgba($color: #4e89ae, $alpha: 0.3);
}
.count-item:nth-of-type(3) {
  background-image: linear-gradient(109.6deg, rgba($color: #679b9b, $alpha: 0.6) 11.2%, #679b9b 100.2%);
  box-shadow: 1px 3px 12px rgba($color: #679b9b, $alpha: 0.3);
}
.count-item:nth-of-type(4) {
  background-image: linear-gradient(109.6deg, rgba($color: #637373, $alpha: 0.6) 11.2%, #637373 100.2%);
  box-shadow: 1px 3px 12px rgba($color: #637373, $alpha: 0.3);
}
</style>