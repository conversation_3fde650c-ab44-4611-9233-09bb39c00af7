import request from '@/utils/request';

export default {
  orderInvoiceList(pagination, searchForm) {
    return request({
      url: '/merchant/orderInvoice/list',
      method: 'post',
      data: {
        ...pagination,
        ...searchForm
      }
    });
  },

  orderInvoiceInfo(id) {
    return request({
      url: '/merchant/orderInvoice/info',
      method: 'post',
      data: {
        id
      }
    });
  },

  updateInvoiceFile(id, filePath) {
    return request({
      url: '/merchant/orderInvoice/updateInvoiceFile',
      method: 'post',
      data: {
        id,
        filePath
      }
    });
  }
};
