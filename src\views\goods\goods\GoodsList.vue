<template>
  <div class="app-container">
    <el-form ref="searchForm" :model="searchForm" label-width="80px">
      <el-row>
        <el-col :span="6">
          <el-form-item label="商品名称" prop="name">
            <el-input v-model="searchForm.name" style="width:200px;" clearable />
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="商品类型" prop="serviceGoods">
            <el-select v-model="searchForm.serviceGoods" placeholder="请选择商品类型" style="width: 220px;">
              <el-option label="服务类" :value="1"></el-option>
              <el-option label="实物商品" :value="2"></el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="商品分类" prop="goodsTypeId">
            <el-select v-model="searchForm.goodsTypeId" placeholder="请选择商品分类" style="width: 220px;">
              <el-option v-for="item in goodsTypeList" :key="item.id" :label="item.name" :value="item.id"></el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item>
            <el-button type="success" icon="search" @click="search">查询</el-button>
            <el-button type="warning" icon="refresh-left" @click="reset">重置</el-button>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <el-row>
      <el-button type="primary" icon="plus" @click="toAdd">新增</el-button>
    </el-row>
    <el-table :data="tableData" border style="margin-top: 10px; " v-loading="loading">
      <el-table-column type="index" label="#" width="55" align="center" />
      <el-table-column prop="name" label="名称">
        <template #default="scope">
          <el-link type="primary" underline="never" @click="toDetail(scope.row)">{{ scope.row.name }}</el-link>
        </template>
      </el-table-column>
      <el-table-column prop="pic" label="图片" width="80">
        <template #default="scope">
          <el-image :z-index="9999" preview-teleported style="width: 50px; height: 50px" :src="scope.row.pic.split(',')[0]"
            :preview-src-list="scope.row.pic.split(',')" :initial-index="0" :teleported="true" fit="cover" />
          <!-- <el-image style="width: 50px; height: 50px" :src="`${scope.row.pic}?imageMogr2/thumbnail/50x`" fit="fill" /> -->
        </template>
      </el-table-column>
      <!-- <el-table-column prop="goodsDesc" label="描述"></el-table-column> -->
      <el-table-column prop="serviceGoods" label="商品类型" width="100">
        <template #default="scope">
          <span v-if="scope.row.serviceGoods === 1" type="success">服务类</span>
          <span v-else-if="scope.row.serviceGoods === 2" type="info">实物商品</span>
        </template>
      </el-table-column>
      <el-table-column prop="goodsTypeName" label="商品分类" width="180"></el-table-column>
      <el-table-column prop="display" label="上架状态" width="100">
        <template #default="scope">
          <el-tag v-if="scope.row.display === 0" type="success">上架中</el-tag>
          <el-tag v-else-if="scope.row.display === 1" type="info">已下架</el-tag>
        </template>
      </el-table-column>
      <!-- <el-table-column prop="sellOut" label="销售状态" width="100">
        <template #default="scope">
          <el-tag v-if="scope.row.sellOut === 0" type="success">正常</el-tag>
          <el-tag v-else-if="scope.row.sellOut === 1" type="info">售罄</el-tag>
        </template>
      </el-table-column> -->

      <el-table-column prop="specType" label="规格类型" width="100">
        <template #default="scope">
          {{ scope.row.specType === 1? "无规格":"多规格" }}
        </template>
      </el-table-column>
      <!-- <el-table-column prop="sort" label="排序"></el-table-column> -->
      <!-- <el-table-column prop="salesVolume" label="虚拟销量"></el-table-column> -->
      <el-table-column prop="createTime" label="创建时间" width="170"></el-table-column>
      <el-table-column fixed="right" label="上架" width="100">
        <template #default="scope">
          <el-button v-if="scope.row.display === 1" type="primary" icon="upload" link @click="changeDisplay(scope.row,0)">上架</el-button>
          <el-button v-else-if="scope.row.display === 0" type="danger" icon="download" link @click="changeDisplay(scope.row,1)">下架</el-button>
        </template>
      </el-table-column>
      <el-table-column fixed="right" label="操作" width="150">
        <template #default="scope">
          <el-button type="primary" link icon="edit" @click="toEdit(scope.row)">修改</el-button>
          <el-button type="danger" link icon="delete" @click="toDel(scope.row)">删除</el-button>
        </template>
      </el-table-column>

    </el-table>
    <el-pagination style="margin-top: 10px;" ref="pagination" :total="pagination.total" v-model:current-page="pagination.current"
      v-model:page-size="pagination.size" :page-sizes="[10, 20, 50]" @size-change="handlePageSizeChange" @current-change="handlePageCurrentChange"
      layout="prev, pager, next, total, sizes">
    </el-pagination>

    <!-- 弹窗, 新增 / 修改 -->
    <!-- <GoodsForm ref="goodsForm" @refreshDataList="refreshList"></GoodsForm> -->

    <GoodsDetails ref="goodsDetails" @refreshDataList="refreshList"></GoodsDetails>
    <!-- 弹窗, 新增 / 修改 -->
    <AuditGoodsForm ref="auditGoodsForm" @refreshDataList="refreshList"></AuditGoodsForm>
  </div>
</template>

<script>
import GoodsDetails from './GoodsDetails.vue';
import AuditGoodsForm from '../auditGoods/AuditGoodsForm.vue';
import GoodsTypeApi from '@/api/GoodsTypeApi';
// import GoodsForm from './GoodsForm.vue';
import Config from '@/settings';
import GoodsApi from '@/api/GoodsApi';
import { ElMessageBox } from 'element-plus';
import { h } from 'vue';

export default {
  name: 'GoodsList',
  data() {
    return {
      tableData: [],
      goodsTypeList: [],
      searchForm: {
        name: '',
        goodsTypeId: '',
        serviceGoods: null
      },
      pagination: {
        total: 0,
        size: Config.pageSize,
        current: 1
      },
      loading: false
    };
  },
  components: { AuditGoodsForm, GoodsDetails },
  activated() {
    GoodsTypeApi.goodsTypeList({ size: -1, current: 1 }).then((data) => {
      this.goodsTypeList = data.records;
    });
    this.refreshList();
  },
  methods: {
    changeDisplay(row, status) {
      GoodsApi.changeDisplay(row.id, status)
        .then(() => {
          this.$message.success('操作成功');
          this.refreshList();
        })
        .catch(() => {});
    },
    toDetail(row) {
      this.$refs.goodsDetails.init(row.id);
    },
    toAdd() {
      this.$refs.auditGoodsForm.init('add', '');
    },
    toEdit(row) {
      this.$refs.auditGoodsForm.init('goodsUpdate', row.id);
    },
    toDel(row) {
      ElMessageBox.confirm(h('p', null, [h('span', null, '确认删除 '), h('i', { style: 'color: red' }, row.name)]), '警告', {
        confirmButtonText: '确认',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          GoodsApi.goodsDelete(row.id).then(() => {
            this.$message.success('删除成功');
            this.refreshList();
          });
        })
        .catch(() => {});
    },
    search() {
      this.pagination.current = 1;
      this.refreshList();
    },
    reset() {
      this.$refs.searchForm.resetFields();
      this.pagination.current = 1;
      this.refreshList();
    },
    refreshList() {
      this.loading = true;
      GoodsApi.goodsList(this.pagination, this.searchForm).then((data) => {
        this.tableData = data.records;
        this.pagination.total = data.total;
        // 删除后如果当前页没数据,则返回前一页
        if (data.records.length === 0 && data.total > 0 && this.pagination.current > 1) {
          this.pagination.current = this.pagination.current - 1;
          this.refreshList();
        }
        this.loading = false;
      });
    },
    handlePageSizeChange() {
      this.pagination.current = 1;
      this.refreshList();
    },
    handlePageCurrentChange() {
      this.refreshList();
    }
  }
};
</script>

<style lang="scss" scoped>
</style>