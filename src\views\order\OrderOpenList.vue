<template>
  <div class="app-container">
    <el-form ref="searchForm" :model="searchForm" label-width="80px">
      <el-row>
        <el-col :span="6">
          <el-form-item label="商品名称" prop="name">
            <el-input v-model="searchForm.name" style="width:200px;" clearable />
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="商品类型" prop="orderTypeId">
            <el-select v-model="searchForm.orderTypeId" placeholder="请选择商品类型">
              <el-option v-for="item in orderTypeList" :key="item.id" :label="item.name" :value="item.id"></el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item>
            <el-button type="success" icon="search" @click="search">查询</el-button>
            <el-button type="warning" icon="refresh-left" @click="reset">重置</el-button>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <el-table :data="tableData" border style="margin-top: 10px; " v-loading="loading">
      <el-table-column type="index" label="#" width="55" align="center" />
      <el-table-column prop="name" label="名称"></el-table-column>
      <el-table-column prop="pic" label="图片">
        <template #default="scope">
          <el-image :z-index="9999" preview-teleported style="width: 50px; height: 50px"
            :src="scope.row.pic.split(',')[0]" :preview-src-list="scope.row.pic.split(',')" :initial-index="0"
            :teleported="true" fit="cover" />
          <!-- <el-image style="width: 50px; height: 50px" :src="`${scope.row.pic}?imageMogr2/thumbnail/50x`" fit="fill" /> -->
        </template>
      </el-table-column>
      <el-table-column prop="orderDesc" label="描述"></el-table-column>
      <el-table-column prop="orderTypeName" label="商品类型"></el-table-column>
      <el-table-column prop="display" label="上架状态">
        <template #default="scope">
          <el-tag v-if="scope.row.display === 0" type="success">上架中</el-tag>
          <el-tag v-else-if="scope.row.display === 1" type="info">已下架</el-tag>
        </template>
      </el-table-column>
      <!-- <el-table-column prop="sellOut" label="销售状态">
        <template #default="scope">
          <el-tag v-if="scope.row.sellOut === 0" type="success">正常</el-tag>
          <el-tag v-else-if="scope.row.sellOut === 1" type="info">售罄</el-tag>
        </template>
      </el-table-column> -->

      <el-table-column prop="specType" label="规格类型">
        <template #default="scope">
          {{ scope.row.specType === 1 ? "统一规格" : "多规格" }}
        </template>
      </el-table-column>
      <el-table-column prop="sort" label="排序"></el-table-column>
      <el-table-column prop="salesVolume" label="虚拟销量"></el-table-column>
      <el-table-column label="是否显示" width="120">
        <template #default="scope">
          <el-tag v-if="scope.row.display === 0" type="success">显示</el-tag>
          <el-tag v-else-if="scope.row.display === 1" type="info">隐藏</el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="createTime" label="创建时间" width="170"></el-table-column>
      <el-table-column label="操作" width="150">
        <template #default="scope">
          <el-button type="danger" icon="delete" @click="toDel(scope.row)" />
        </template>
      </el-table-column>

    </el-table>
    <el-pagination style="margin-top: 10px;" ref="pagination" :total="pagination.total"
      v-model:current-page="pagination.current" v-model:page-size="pagination.size" :page-sizes="[10, 20, 50]"
      @size-change="handlePageSizeChange" @current-change="handlePageCurrentChange"
      layout="prev, pager, next, total, sizes">
    </el-pagination>

  </div>
</template>

<script>
import Config from '@/settings';
import OrderApi from '@/api/OrderApi';
import { ElMessageBox } from 'element-plus';
import { h } from 'vue';

export default {
  name: 'OrderOpenList',
  data() {
    return {
      tableData: [],
      orderTypeList: [],
      searchForm: {
        name: '',
        orderTypeId: ''
      },
      pagination: {
        total: 0,
        size: Config.pageSize,
        current: 1
      },
      loading: false
    };
  },
  components: {},
  activated() {
    this.refreshList();
  },
  methods: {
    toDel(row) {
      ElMessageBox.confirm(h('p', null, [h('span', null, '确认删除 '), h('i', { style: 'color: red' }, row.name)]), '警告', {
        confirmButtonText: '确认',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          OrderApi.orderDelete(row.id).then(() => {
            this.$message.success('删除成功');
            this.refreshList();
          });
        })
        .catch(() => { });
    },
    search() {
      this.pagination.current = 1;
      this.refreshList();
    },
    reset() {
      this.$refs.searchForm.resetFields();
      this.pagination.current = 1;
      this.refreshList();
    },
    refreshList() {
      this.loading = true;
      this.searchForm.status = 1;
      OrderApi.orderList(this.pagination, this.searchForm).then((data) => {
        this.tableData = data.records;
        this.pagination.total = data.total;
        // 删除后如果当前页没数据,则返回前一页
        if (data.records.length === 0 && data.total > 0 && this.pagination.current > 1) {
          this.pagination.current = this.pagination.current - 1;
          this.refreshList();
        }
        this.loading = false;
      });
    },
    handlePageSizeChange() {
      this.pagination.current = 1;
      this.refreshList();
    },
    handlePageCurrentChange() {
      this.refreshList();
    }
  }
};
</script>

<style lang="scss" scoped></style>