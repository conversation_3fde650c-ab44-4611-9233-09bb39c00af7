import request from '@/utils/request';

export default {
  financialFlowList(pagination, searchForm) {
    return request({
      url: '/merchant/financialFlow/list',
      method: 'post',
      data: {
        ...pagination,
        ...searchForm
      }
    });
  },

  financialFlowInfo(id) {
    return request({
      url: '/merchant/financialFlow/info',
      method: 'post',
      data: {
        id
      }
    });
  }
};
