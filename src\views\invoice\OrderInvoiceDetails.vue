<template>
  <div>
    <el-dialog v-model="visible" title="开票信息" width="1200px" style="margin-top: 50px;" :close-on-click-modal="false" draggable>
      <el-form ref="inputForm" :model="inputForm" v-loading="loading" label-width="120px" label-position="top">
        <el-tabs v-model="activeName">

          <el-tab-pane label="开票信息" :name="0">

            <el-row :gutter="15">
              <el-col :span="6">
                <el-form-item label="订单号" prop="orderCode">
                  <div class="info-value">{{ inputForm.orderCode }}</div>
                </el-form-item>
              </el-col>
              <el-col :span="6">
                <el-form-item label="抬头类型" prop="titleType">
                  <div class="info-value">{{ inputForm.titleType }}</div>
                </el-form-item>
              </el-col>
              <el-col :span="6">
                <el-form-item label="发票类型" prop="invoiceType">
                  <div class="info-value">{{ inputForm.invoiceType===1?"增值税普通发票（普票）":"增值税专用发票（专票）" }}</div>
                </el-form-item>
              </el-col>

              <el-col :span="6">
                <el-form-item label="开票状态" prop="status">
                  <div class="info-value">
                    <el-tag v-if="inputForm.status === 0" type="warning">待开</el-tag>
                    <el-tag v-else-if="inputForm.status === 1" type="success">已开</el-tag>
                  </div>
                </el-form-item>
              </el-col>
              <el-col :span="6">
                <el-form-item label="发票抬头" prop="titleName">
                  <div class="info-value">{{ inputForm.titleName }}</div>
                </el-form-item>
              </el-col>

              <el-col :span="6">
                <el-form-item label="纳税人识别号" prop="taxId">
                  <div class="info-value">{{ inputForm.taxId }}</div>
                </el-form-item>
              </el-col>
              <el-col :span="6">
                <el-form-item label="企业注册地址" prop="registeredAddress">
                  <div class="info-value">{{ inputForm.registeredAddress }}</div>
                </el-form-item>
              </el-col>
              <el-col :span="6">
                <el-form-item label="企业注册电话" prop="registeredPhone">
                  <div class="info-value">{{ inputForm.registeredPhone }}</div>
                </el-form-item>
              </el-col>
              <el-col :span="6">
                <el-form-item label="基本开户银行" prop="bankName">
                  <div class="info-value">{{ inputForm.bankName }}</div>
                </el-form-item>
              </el-col>
              <el-col :span="6">
                <el-form-item label="基本开户账号" prop="bankAccount">
                  <div class="info-value">{{ inputForm.bankAccount }}</div>
                </el-form-item>
              </el-col>
              <el-col :span="6">
                <el-form-item label="接收邮箱" prop="receiveEmail">
                  <div class="info-value">{{ inputForm.receiveEmail }}</div>
                </el-form-item>
              </el-col>

              <el-col :span="6">
                <el-form-item label="申请时间" prop="createTime">
                  <div class="info-value">{{ inputForm.createTime }}</div>
                </el-form-item>
              </el-col>

            </el-row>
          </el-tab-pane>
        </el-tabs>

      </el-form>

      <el-divider />

      <div style="text-align: center;" v-if="inputForm.status === 0">

        <el-upload drag :action="uploadUrl" :headers="headers" :on-success="handleSuccess">
          <el-icon class="el-icon--upload"><upload-filled /></el-icon>
          <div class="el-upload__text">
            可拖拽文件到这里 <em>或点击上传</em>
          </div>
          <template #tip>
            <div class="el-upload__tip">
              最大上传文件为 <strong>10MB</strong>
            </div>
          </template>
        </el-upload>

        <el-button style="margin-top: 20px;" size="large" type="primary" @click="toUpdate">提交发票</el-button>
      </div>
      <div v-else>
        <el-link type="primary" underline="never" :href="inputForm.filePath" target="_blank">点击下载发票</el-link>
      </div>

      <template #footer>
        <span class="dialog-footer">
          <el-button size="large" @click="visible = false">关闭</el-button>
        </span>
      </template>

    </el-dialog>
  </div>
</template>

<script>
import OrderInvoiceApi from '@/api/OrderInvoiceApi';
import FileUploadApi from '@/api/FileUploadApi';
import { mapGetters } from 'vuex';

export default {
  name: 'OrderInvoiceDetails',
  props: {},
  data() {
    return {
      uploadUrl: FileUploadApi.merchantFileUpload(),
      activeName: 0,
      title: '',
      nonull: { required: true, message: '必填' },
      visible: false,
      loading: false,
      inputForm: {
        id: '',
        orderCode: '',
        titleType: '',
        invoiceType: '',
        titleName: '',
        taxId: '',
        registeredAddress: '',
        registeredPhone: '',
        bankName: '',
        bankAccount: '',
        receiveEmail: '',
        status: '',
        amount: '',
        filePath: ''
      },
      headers: {}
    };
  },
  components: {},
  computed: {
    ...mapGetters('app', ['authToken'])
  },
  created() {
    this.headers['Authorization'] = this.authToken;
  },
  mounted() {},
  methods: {
    toUpdate() {
      this.loading = true;
      OrderInvoiceApi.updateInvoiceFile(this.inputForm.id, this.inputForm.filePath)
        .then((data) => {
          this.$message.success('操作成功');
          this.loading = false;
          this.visible = false;
          this.$emit('refreshDataList');
        })
        .catch(() => {
          this.loading = false;
        });
    },
    handleSuccess(response) {
      this.inputForm.filePath = response.path;
    },
    toSendDelivery() {
      this.$refs.sendDelivery.init(this.inputForm.orderCode);
    },
    init(id) {
      this.inputForm.id = id;
      this.visible = true;
      this.loading = false;
      this.$nextTick(() => {
        this.$refs.inputForm.resetFields();
        this.loading = true;
        OrderInvoiceApi.orderInvoiceInfo(this.inputForm.id).then((data) => {
          this.inputForm = this.$utils.recover(this.inputForm, data);
          this.loading = false;
        });
      });
    }
  }
};
</script>

<style lang="scss" scoped>
.info-value {
  font-size: 16px;
  font-weight: bold;
  color: #333;
}

.info-img {
  border-radius: 6pxs;
  border: 1px solid #ddd;
  width: 150px;
  height: 150px;
  margin-right: 20px;
}
</style>