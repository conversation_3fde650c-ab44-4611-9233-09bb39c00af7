/**
 * 表单对象赋值:
 * 对目标对象存在且源对象同样存在的属性，全部覆盖；
 * 目标对象不存在但是源对象存在的属性， 全部丢弃；
 * 目标对象存在但是源对象不存在的属性，如果是字符串赋值为空串，其余类型赋值为undefined
 */
export function recover(target, source) {
  if (target === undefined || target === null) {
    throw new TypeError('Cannot convert first argument to object');
  }
  var to = Object(target);
  if (source === undefined || source === null) {
    return to;
  }
  var keysArray = Object.keys(Object(target));
  for (var nextIndex = 0, len = keysArray.length; nextIndex < len; nextIndex++) {
    var nextKey = keysArray[nextIndex];
    var desc = Object.getOwnPropertyDescriptor(target, nextKey);
    if (desc !== undefined && desc.enumerable) {
      /* eslint-disable no-prototype-builtins */
      if (to.hasOwnProperty(nextKey)) {
        if (to[nextKey] instanceof Array) {
          to[nextKey] = source[nextKey];
        } else if (to[nextKey] instanceof Object) {
          recover(to[nextKey], source[nextKey]);
        } else if (source[nextKey] !== undefined) {
          to[nextKey] = source[nextKey];
        } else if (typeof to[nextKey] === 'string') {
          to[nextKey] = '';
        } else {
          to[nextKey] = undefined;
        }
      }
    }
  }
  return to;
}

/**
 * 表单对象赋值:
 * 对目标对象存在且源对象同样存在的属性，全部覆盖；
 * 目标对象不存在但是源对象存在的属性， 全部丢弃；
 * 目标对象存在但是源对象不存在的属性，保留目标对象的属性不做处理
 */
export function recoverNotNull(target, source) {
  if (target === undefined || target === null) {
    throw new TypeError('Cannot convert first argument to object');
  }
  var to = Object(target);
  if (source === undefined || source === null) {
    return to;
  }
  var keysArray = Object.keys(Object(target));
  for (var nextIndex = 0, len = keysArray.length; nextIndex < len; nextIndex++) {
    var nextKey = keysArray[nextIndex];
    var desc = Object.getOwnPropertyDescriptor(target, nextKey);
    if (desc !== undefined && desc.enumerable) {
      /* eslint-disable no-prototype-builtins */
      if (to.hasOwnProperty(nextKey)) {
        if (to[nextKey] instanceof Array) {
          to[nextKey] = source[nextKey];
        } else if (to[nextKey] instanceof Object) {
          recover(to[nextKey], source[nextKey]);
        } else if (source[nextKey] !== undefined) {
          to[nextKey] = source[nextKey];
        }
      }
    }
  }
  return to;
}

export const deepTree = (list) => {
  let newList = [];
  let map = {};

  list.forEach((e) => (map[e.id] = e));

  list.forEach((e) => {
    let parent = map[e.parentId];

    if (parent) {
      (parent.children || (parent.children = [])).push(e);
    } else {
      if (!e.parentId) {
        newList.push(e);
      }
    }
  });

  const fn = (list) => {
    list.map((e) => {
      if (e.children instanceof Array) {
        e.children = orderBy(e.children, 'id');
        fn(e.children);
      }
    });
  };

  fn(newList);

  return orderBy(newList, 'id');
};

export function orderBy(list, key) {
  return list.sort((a, b) => a[key] - b[key]);
}

export const revisePath = (path) => {
  if (!path) {
    return '';
  }

  if (path[0] == '/') {
    return path;
  } else {
    return `/${path}`;
  }
};

export const isNull = (value) => {
  return value === '' || value === undefined || value === null;
};

export default { recover, recoverNotNull, deepTree, revisePath, isNull };
