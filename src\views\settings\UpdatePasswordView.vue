<template>
  <div>
    <el-form ref="loginForm" :model="loginForm" :rules="formRules" label-position="left" label-width="100px" class="login-form">
      <el-form-item prop="oldPwd" label="原密码">
        <el-input v-model="loginForm.oldPwd" type="password" auto-complete="off" placeholder="原密码">
          <template #prefix>
            <i class="fas fa-user" />
          </template>
        </el-input>
      </el-form-item>
      <el-form-item prop="newPwd" label="新密码">
        <el-input v-model="loginForm.newPwd" type="password" auto-complete="off" placeholder="新密码">
          <template #prefix>
            <i class="fas fa-lock" />
          </template>
        </el-input>
      </el-form-item>
      <el-form-item prop="newPwd2" label="确认密码">
        <el-input v-model="loginForm.newPwd2" type="password" auto-complete="off" placeholder="确认密码">
          <template #prefix>
            <i class="fas fa-lock" />
          </template>
        </el-input>
      </el-form-item>
      <el-form-item>
        <div style="display: flex;flex-direction: row;justify-content: space-around;margin-top: 20px;">
          <el-button :loading="loading" type="primary" style="width:100%;" @click.prevent="doSubmit">提 交</el-button>
        </div>
      </el-form-item>

    </el-form>

  </div>
</template>

<script>
import AuthApi from '@/api/AuthApi';
import { ElMessage, ElMessageBox } from 'element-plus';

export default {
  name: 'updatePassword',
  data() {
    return {
      loginForm: {
        oldPwd: '',
        newPwd: '',
        newPwd2: ''
      },

      formRules: {
        oldPwd: [{ required: true, message: '必填' }],
        newPwd: [
          { required: true, message: '必填' },
          { validator: this.validatePass, trigger: 'blur' }
        ],
        newPwd2: [
          { required: true, message: '必填' },
          { validator: this.validatePass, trigger: 'blur' }
        ]
      },
      loading: false
    };
  },
  components: {},
  created() {},
  methods: {
    validatePass(rule, value, callback) {
      if (value === '') {
        callback(new Error('必填'));
      } else {
        if (this.loginForm.newPwd != this.loginForm.newPwd2) {
          callback(new Error('两次密码输入不一致'));
        } else {
          if (rule.field == 'newPwd2') {
            this.$refs.loginForm.validateField('newPwd');
          }
          callback();
        }
      }
    },
    doSubmit() {
      this.$refs['inputForm'].validate((valid) => {
        if (valid) {
          this.loading = true;
          AuthApi.updatePwd(this.loginForm.oldPwd, this.loginForm.newPwd)
            .then(() => {
              ElMessageBox.alert('密码修改成功，请重新登录', '提示', {
                confirmButtonText: '确认',
                callback: () => {
                  this.$store.commit('app/logout');
                  this.$router.replace('/Login');
                }
              });
            })
            .catch((error) => {
              console.log(error);
              ElMessage.error((error.response.data && error.response.data.msg) || '请求失败');
            })
            .finally(() => {
              this.loading = false;
            });
        }
      });
    }
  }
};
</script>

<style scoped lang="scss">
.login-form {
  border-radius: 6px;
  background: #ffffff;
  width: 385px;
  padding: 25px 25px 5px 25px;
  margin-bottom: 150px;
}
</style>
