<template>
  <div>
    <el-dialog v-model="visible" :title="title" width="75%" :close-on-click-modal="false" draggable>
      <el-form ref="inputForm" :model="inputForm" v-loading="loading" :class="method === 'view' ? 'readonly' : ''" :disabled="method === 'view'" label-width="120px">
        <el-tabs v-model="activeName" type="border-card">
          <el-tab-pane label="基本信息" name="info">
            <el-row :gutter="15">
              <el-col :span="12">
                <el-form-item label="商品名称" prop="name" :rules="[nonull]">
                  <el-input v-model="inputForm.name" placeholder="请输入类型名称"></el-input>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="商品类型" prop="goodsTypeId" :rules="[nonull]">
                  <el-select v-model="inputForm.goodsTypeId" placeholder="请选择商品类型">
                    <el-option v-for="item in goodsTypeList" :key="item.id" :label="item.name" :value="item.id"></el-option>
                  </el-select>
                </el-form-item>
              </el-col>

              <el-col :span="24">
                <el-form-item label="商品描述" prop="goodsDesc" :rules="[nonull]">
                  <el-input v-model="inputForm.goodsDesc" :rows="2" type="textarea" show-word-limit maxlength="100" placeholder="请输入描述"></el-input>
                </el-form-item>
              </el-col>

              <el-col :span="24">
                <el-form-item label="图片" prop="pic" :rules="[nonull]">
                  <el-upload v-model:file-list="fileList" :headers="headers" action="/api/file/upload" list-type="picture-card" :on-success="handleSuccess"
                    :on-remove="handleRemove" :limit="5">
                    <el-icon v-if="fileList.length < 5">
                      <Plus />
                    </el-icon>
                  </el-upload>
                </el-form-item>
              </el-col>

              <el-col :span="12">
                <el-form-item label="是否上架" prop="display" :rules="[nonull]">
                  <el-radio-group v-model="inputForm.display">
                    <el-radio :value="0">是</el-radio>
                    <el-radio :value="1">否</el-radio>
                  </el-radio-group>
                </el-form-item>
              </el-col>
              <!-- <el-col :span="12">
                <el-form-item label="售卖状态" prop="sellOut" :rules="[nonull]">
                  <el-radio-group v-model="inputForm.sellOut">
                    <el-radio :value="0">正常</el-radio>
                    <el-radio :value="1">已售罄</el-radio>
                  </el-radio-group>
                </el-form-item>
              </el-col> -->

              <el-col :span="12">
                <el-form-item label="虚拟销量" prop="salesVolume" :rules="[nonull]">
                  <el-input-number v-model="inputForm.salesVolume" :min="0" placeholder="请输入虚拟销量" />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="排序" prop="sort" :rules="[nonull]">
                  <el-input-number v-model="inputForm.sort" :min="1" placeholder="请输入排序" />
                </el-form-item>
              </el-col>
            </el-row>
          </el-tab-pane>

          <el-tab-pane label="规格设置" name="spec">
            <el-row>
              <el-col :span="12">
                <el-form-item label="规格类型" prop="specType" :rules="[nonull]" @change="onSpecTypeChange">
                  <el-radio-group v-model="inputForm.specType">
                    <el-radio :value="1">统一规格</el-radio>
                    <!-- <el-radio :label="2">多规格</el-radio> -->
                  </el-radio-group>
                </el-form-item>
              </el-col>
            </el-row>

            <el-row v-if="inputForm.specType === 1">
              <el-col :span="12">
                <el-form-item label="实际售价" prop="price" :rules="[nonull]">
                  <el-input-number v-model="inputForm.price" :min="1" placeholder="请输入实际售价" />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="原价" prop="oldPrice" :rules="[nonull]">
                  <el-input-number v-model="inputForm.oldPrice" :min="1" placeholder="请输入原价" />
                </el-form-item>
              </el-col>
            </el-row>

          </el-tab-pane>

        </el-tabs>

      </el-form>

      <template #footer>
        <span class="dialog-footer">
          <el-button size="large" @click="visible = false">关闭</el-button>
          <el-button size="large" type="success" v-if="method !== 'view'" :loading="loading" @click="doSubmit()">确定</el-button>
        </span>
      </template>

    </el-dialog>
  </div>
</template>

<script>
import GoodsTypeApi from '@/api/GoodsTypeApi';
import GoodsApi from '@/api/GoodsApi';
import { mapGetters } from 'vuex';

export default {
  data() {
    return {
      title: '',
      activeName: 'info',
      nonull: { required: true, message: '必填' },
      method: '',
      visible: false,
      loading: false,
      inputForm: {
        id: '',
        name: '',
        goodsTypeId: null,
        goodsDesc: '',
        pic: [],
        display: 0,
        sellOut: 0,
        salesVolume: 0,
        sort: 0,
        specType: 1,
        specData: [],
        price: undefined,
        oldPrice: undefined
      },
      goodsTypeList: [],
      headers: [],
      fileList: []
    };
  },
  components: {},
  computed: {
    ...mapGetters('app', ['authToken'])
  },
  created() {
    this.headers['Authorization'] = this.authToken;
  },
  activated() {
    this.activeName = 'info';
    GoodsTypeApi.goodsTypeList({ size: -1, current: 1 }).then((data) => {
      this.goodsTypeList = data.records;
    });
  },
  methods: {
    onSpecTypeChange() {},
    handleRemove() {
      this.inputForm.pic = this.fileList.map((item) => item.url);
    },
    handleSuccess(response) {
      this.inputForm.pic.push(response.path);
    },
    init(method, id) {
      this.method = method;
      this.inputForm.id = id;
      if (method === 'add') {
        this.title = '新建';
      } else if (method === 'edit') {
        this.title = '修改';
      } else if (method === 'view') {
        this.title = '查看';
      }
      this.visible = true;
      this.loading = false;
      this.$nextTick(() => {
        this.$refs.inputForm.resetFields();
        this.fileList = [];
        if (method === 'edit' || method === 'view') {
          // 修改或者查看
          this.loading = true;
          GoodsApi.goodsInfo(this.inputForm.id).then((data) => {
            this.inputForm = this.$utils.recover(this.inputForm, data);
            if (this.inputForm.specType === 1) {
              const goodsItem = data.goodsItemList[0];
              this.inputForm.price = Number(goodsItem.price);
              this.inputForm.oldPrice = Number(goodsItem.oldPrice);
            }

            this.inputForm.pic = this.inputForm.pic.split(',');
            this.fileList = this.inputForm.pic.map((element) => {
              return {
                name: 'food.jpeg',
                url: element
              };
            });
            this.loading = false;
          });
        }
      });
    },
    // 表单提交
    doSubmit() {
      this.$refs['inputForm'].validate((valid) => {
        if (valid) {
          this.loading = true;
          const params = Object.assign({}, this.inputForm);
          params.pic = params.pic.join(',');
          if (params.specType === 1) {
            params.specData = [{ price: params.price, oldPrice: params.oldPrice }];
          }

          GoodsApi.goodsSave(params)
            .then(() => {
              this.visible = false;
              this.$message.success('操作成功');
              this.$emit('refreshDataList');
              this.loading = false;
            })
            .catch(() => {
              this.loading = false;
            });
        }
      });
    }
  }
};
</script>