import { menuData } from '@/router/menu.js';

const state = {
  visitedViews: [],
  cachedViews: []
};

const mutations = {
  INIT: (state) => {
    state.cachedViews = [
      {
        keepAlive: menuData[0].meta.keepAlive,
        title: menuData[0].meta.title,
        icon: menuData[0].meta.icon,
        path: menuData[0].path,
        name: menuData[0].name
      }
    ];
  },
  ADD_VIEW: (state, view) => {
    if (state.cachedViews.some((v) => v.path === view.path || v.name === view.name)) {
      return;
    }
    state.cachedViews.push(Object.assign({}, view));
  },
  DEL_VIEW: (state, index) => {
    state.cachedViews.splice(index, 1);
  }
};

const getters = {
  cachedViews: (state) => state.cachedViews
};

const actions = {};

export default {
  namespaced: true,
  state,
  mutations,
  getters,
  actions
};
