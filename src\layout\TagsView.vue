<template>
  <div ref="container" class="tags-wrap">
    <div v-for="(item, index) in cachedViews" :key="index" :class="{ active: isActive(item) }" :data-index="index" class="block" @mousedown="(e) => {onTap(e, item);}">
      <span>{{ item.title }}</span>

      <el-icon v-if="index > 0" @mousedown.stop="onDel(index)">
        <CircleCloseFilled />
      </el-icon>

    </div>

  </div>
</template>

<script>
import { mapGetters } from 'vuex';

export default {
  name: 'TagsView',
  components: {
    // Breadcrumb,
    // HamBurger
  },
  data() {
    return {
      menu: {
        visible: false,
        current: {},
        style: {
          left: 0,
          top: 0
        }
      },
      isHit: false
    };
  },
  computed: {
    ...mapGetters('tags', ['cachedViews'])
  },
  methods: {
    isActive(item) {
      return item.path === this.$route.path || item.name === this.$route.name;
    },
    onTap(e, item) {
      this.isHit = item.active;
      if (e.button == 0) {
        this.$router.push(item.path);
      } else {
        this.menu = {
          current: item,
          visible: true,
          style: {
            left: e.layerX + 'px',
            top: e.layerY + 'px'
          }
        };
      }
    },
    onDel(index) {
      const active = this.cachedViews.find((e) => e.path === this.$route.path);
      if (active && this.cachedViews.indexOf(active) === index) {
        const next = this.cachedViews[this.cachedViews.indexOf(active) - 1];
        this.$router.push(next ? next.path : '/');
      }
      this.$store.commit('tags/DEL_VIEW', index);
    }
  }
};
</script>

<style lang="scss" scoped>
.tags-wrap {
  background-color: #eaeaea;
  display: flex;
  height: 30px;
  position: relative;
  padding: 10px 10px;

  .block {
    display: inline-block;
    border-radius: 3px;
    height: 30px;
    line-height: 30px;
    padding: 0 10px;
    background-color: #fff;
    font-size: 12px;
    margin-right: 10px;
    color: #909399;
    cursor: pointer;

    &:last-child {
      margin-right: 0;
    }

    i {
      font-size: 14px;
      position: relative;
      top: 3px;
      width: 0;
      overflow: hidden;
      transition: all 0.3s;

      &:hover {
        color: #333;
        background-color: #fff;
      }
    }

    &:hover {
      .el-icon {
        width: auto;
        margin-left: 5px;
      }
    }

    &.active {
      span {
        color: #4165d7;
      }

      i {
        width: auto;
        margin-left: 5px;
      }

      &:before {
        background-color: #4165d7;
      }
    }
  }
}
</style>
