<template>
  <div class="app-container">
    <el-form ref="searchForm" :model="searchForm" label-width="80px">
      <el-row>
        <el-col :span="6">
          <el-form-item label="商品名称" prop="name">
            <el-input v-model="searchForm.name" style="width:200px;" clearable />
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="商品类型" prop="serviceGoods">
            <el-select v-model="searchForm.serviceGoods" placeholder="请选择商品类型">
              <el-option label="服务类" value="1"></el-option>
              <el-option label="实物商品" value="2"></el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="商品分类" prop="goodsTypeId">
            <el-select v-model="searchForm.goodsTypeId" placeholder="请选择商品分类">
              <el-option v-for="item in goodsTypeList" :key="item.id" :label="item.name" :value="item.id"></el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item>
            <el-button type="success" icon="search" @click="search">查询</el-button>
            <el-button type="warning" icon="refresh-left" @click="reset">重置</el-button>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <!-- <el-row>
      <el-button type="primary" icon="plus" @click="toAdd">新增</el-button>
    </el-row> -->
    <el-table :data="tableData" border style="margin-top: 10px; " v-loading="loading">
      <el-table-column type="index" label="#" width="55" align="center" />
      <el-table-column prop="name" label="名称">
        <template #default="scope">
          <el-link type="primary" underline="never" @click="toDetail(scope.row)">{{ scope.row.name }}</el-link>
        </template>
      </el-table-column>
      <el-table-column prop="pic" label="图片" width="100">
        <template #default="scope">
          <el-image :z-index="9999" preview-teleported style="width: 50px; height: 50px" :src="scope.row.pic.split(',')[0]"
            :preview-src-list="scope.row.pic.split(',')" :initial-index="0" :teleported="true" fit="cover" />
          <!-- <el-image style="width: 50px; height: 50px" :src="`${scope.row.pic}?imageMogr2/thumbnail/50x`" fit="fill" /> -->
        </template>
      </el-table-column>
      <!-- <el-table-column prop="goodsDesc" label="描述"></el-table-column> -->
      <el-table-column prop="serviceGoods" label="商品类型" width="100">
        <template #default="scope">
          <span v-if="scope.row.serviceGoods === 1">服务类</span>
          <span v-else-if="scope.row.serviceGoods === 2">实物商品</span>
        </template>
      </el-table-column>
      <el-table-column prop="goodsTypeName" label="商品分类" width="100"></el-table-column>

      <el-table-column prop="display" label="上架状态" width="100">
        <template #default="scope">
          <span v-if="scope.row.display === 0">立即上架</span>
          <span v-else-if="scope.row.display === 1">手动上架</span>
        </template>
      </el-table-column>

      <el-table-column label="审核类型" width="100">
        <template #default="scope">
          <span v-if="scope.row.auditType === 1">新增商品</span>
          <span v-else-if="scope.row.auditType === 2">修改商品</span>
        </template>
      </el-table-column>
      <el-table-column label="审核状态" width="110">
        <template #default="scope">
          <el-tag v-if="scope.row.auditStatus === 0" type="warning">待审核</el-tag>
          <el-tag v-else-if="scope.row.auditStatus === 1" type="success">审核通过</el-tag>
          <el-tag v-else-if="scope.row.auditStatus === 2" type="info">审核不通过</el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="auditComment" label="审核意见" width="200"></el-table-column>
      <el-table-column prop="createTime" label="创建时间" width="140">
        <template #default="scope">
          {{ scope.row.createTime.substring(0, 16) }}
        </template>
      </el-table-column>
      <el-table-column label="操作" width="150">
        <template #default="scope">
          <el-button v-if="scope.row.auditStatus === 0" type="primary" link icon="edit" @click="toEdit(scope.row)">修改</el-button>
          <el-button v-if="scope.row.auditStatus === 0" type="danger" link icon="delete" @click="toDel(scope.row)">删除</el-button>
        </template>
      </el-table-column>

    </el-table>
    <el-pagination style="margin-top: 10px;" ref="pagination" :total="pagination.total" v-model:current-page="pagination.current"
      v-model:page-size="pagination.size" :page-sizes="[10, 20, 50]" @size-change="handlePageSizeChange"
      @current-change="handlePageCurrentChange" layout="prev, pager, next, total, sizes">
    </el-pagination>

    <AuditGoodsDetails ref="auditGoodsDetails" @refreshDataList="refreshList"></AuditGoodsDetails>
    <!-- 弹窗, 新增 / 修改 -->
    <AuditGoodsForm ref="auditGoodsForm" @refreshDataList="refreshList"></AuditGoodsForm>
  </div>
</template>

<script>
import GoodsTypeApi from '@/api/GoodsTypeApi';
import AuditGoodsForm from './AuditGoodsForm.vue';
import AuditGoodsDetails from './AuditGoodsDetails.vue';
import Config from '@/settings';
import AuditGoodsApi from '@/api/AuditGoodsApi';
import { ElMessageBox } from 'element-plus';
import { h } from 'vue';

export default {
  name: 'AuditGoodsList',
  data() {
    return {
      tableData: [],
      goodsTypeList: [],
      searchForm: {
        name: '',
        goodsTypeId: '',
        serviceGoods: ''
      },
      pagination: {
        total: 0,
        size: Config.pageSize,
        current: 1
      },
      loading: false
    };
  },
  components: { AuditGoodsForm, AuditGoodsDetails },
  activated() {
    GoodsTypeApi.goodsTypeList({ size: -1, current: 1 }).then((data) => {
      this.goodsTypeList = data.records;
    });
    this.refreshList();
  },
  methods: {
    toDetail(row) {
      this.$refs.auditGoodsDetails.init(row.id);
    },
    toAdd() {
      this.$refs.auditGoodsForm.init('add', '');
    },
    toEdit(row) {
      this.$refs.auditGoodsForm.init('edit', row.id);
    },
    toDel(row) {
      ElMessageBox.confirm(h('p', null, [h('span', null, '确认删除 '), h('i', { style: 'color: red' }, row.name)]), '警告', {
        confirmButtonText: '确认',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          AuditGoodsApi.auditGoodsDelete(row.id).then(() => {
            this.$message.success('删除成功');
            this.refreshList();
          });
        })
        .catch(() => {});
    },
    search() {
      this.pagination.current = 1;
      this.refreshList();
    },
    reset() {
      this.$refs.searchForm.resetFields();
      this.pagination.current = 1;
      this.refreshList();
    },
    refreshList() {
      this.loading = true;
      AuditGoodsApi.auditGoodsList(this.pagination, this.searchForm).then((data) => {
        this.tableData = data.records;
        this.pagination.total = data.total;
        // 删除后如果当前页没数据,则返回前一页
        if (data.records.length === 0 && data.total > 0 && this.pagination.current > 1) {
          this.pagination.current = this.pagination.current - 1;
          this.refreshList();
        }
        this.loading = false;
      });
    },
    handlePageSizeChange() {
      this.pagination.current = 1;
      this.refreshList();
    },
    handlePageCurrentChange() {
      this.refreshList();
    }
  }
};
</script>

<style lang="scss" scoped>
</style>