import request from '@/utils/request';

export default {
  goodsSpecList(pagination, searchForm) {
    return request({
      url: '/merchant/goodsSpec/list',
      method: 'post',
      data: {
        ...pagination,
        ...searchForm
      }
    });
  },

  goodsSpecInfo(id) {
    return request({
      url: '/merchant/goodsSpec/info',
      method: 'post',
      data: {
        id
      }
    });
  },

  goodsSpecDelete(id) {
    return request({
      url: '/merchant/goodsSpec/delete',
      method: 'post',
      data: {
        id
      }
    });
  },

  goodsSpecSave(data) {
    if (!data.id) {
      return request({
        url: '/merchant/goodsSpec/add',
        method: 'post',
        data: {
          ...data
        }
      });
    } else {
      return request({
        url: '/merchant/goodsSpec/update',
        method: 'post',
        data: {
          ...data
        }
      });
    }
  }
};
