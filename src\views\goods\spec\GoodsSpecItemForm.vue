<template>
  <div>
    <el-dialog v-model="visible" :title="title" width="50%" :close-on-click-modal="false" draggable>
      <el-form ref="inputForm" :model="inputForm" v-loading="loading" :class="method==='view'?'readonly':''" :disabled="method==='view'" label-width="120px">
        <el-row :gutter="15">
          <el-col :span="12">
            <el-form-item label="名称" prop="name" :rules="[nonull]">
              <el-input v-model="inputForm.name" placeholder="请输入名称"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="描述" prop="remark">
              <el-input v-model="inputForm.remark" :rows="2" type="textarea" show-word-limit maxlength="100" placeholder="请输入描述"></el-input>
            </el-form-item>
          </el-col>

        </el-row>

      </el-form>

      <template #footer>
        <span class="dialog-footer">
          <el-button size="large" @click="visible = false">关闭</el-button>
          <el-button size="large" type="success" v-if="method !== 'view'" :loading="loading" @click="doSubmit()">确定</el-button>
        </span>
      </template>

    </el-dialog>
  </div>
</template>

<script>
import GoodsSpecItemApi from '@/api/GoodsSpecItemApi';

export default {
  data() {
    return {
      title: '',
      nonull: { required: true, message: '必填' },
      method: '',
      visible: false,
      loading: false,
      inputForm: {
        id: '',
        goodsSpecId: '',
        name: '',
        remark: ''
      },
      headers: [],
      fileList: []
    };
  },
  components: {},
  computed: {},
  created() {},
  methods: {
    handleRemove() {
      this.inputForm.pic = '';
    },
    handleSuccess(response) {
      this.inputForm.pic = response.path;
    },
    init(method, goodsSpecId, id) {
      this.method = method;
      this.inputForm.id = id;
      this.inputForm.goodsSpecId = goodsSpecId;
      if (method === 'add') {
        this.title = '新建';
      } else if (method === 'edit') {
        this.title = '修改';
      } else if (method === 'view') {
        this.title = '查看';
      }
      this.visible = true;
      this.loading = false;
      this.$nextTick(() => {
        this.$refs.inputForm.resetFields();
        if (method === 'edit' || method === 'view') {
          // 修改或者查看
          this.loading = true;
          GoodsSpecItemApi.goodsSpecItemInfo(this.inputForm.id).then((data) => {
            this.inputForm = this.$utils.recover(this.inputForm, data);
            this.loading = false;
          });
        }
      });
    },
    // 表单提交
    doSubmit() {
      this.$refs['inputForm'].validate((valid) => {
        if (valid) {
          this.loading = true;
          GoodsSpecItemApi.goodsSpecItemSave(this.inputForm)
            .then(() => {
              this.visible = false;
              this.$message.success('操作成功');
              this.$emit('refreshDataList');
              this.loading = false;
            })
            .catch(() => {
              this.loading = false;
            });
        }
      });
    }
  }
};
</script>