<template>
  <div>
    <el-dialog v-model="visible" title="入驻申请" width="1200px" style="margin-top: 50px;" :close-on-click-modal="false" draggable>
      <el-tabs type="border-card">
        <el-tab-pane label="入驻申请">
          <el-form ref="inputForm" :model="inputForm" v-loading="loading" label-width="120px" label-position="top">
            <el-row :gutter="15">

              <el-col :span="8">
                <el-form-item label="手机号" prop="mobile" :rules="[nonull]">
                  <el-input v-model="inputForm.mobile" placeholder="请输入手机号" style="width: 300px;"></el-input>
                </el-form-item>
              </el-col>

              <el-col :span="8">
                <el-form-item label="短信验证码" prop="smsCode" :rules="[nonull]">
                  <el-input v-model="inputForm.smsCode" placeholder="请输入验证码" style="width: 150px;"></el-input>
                  <el-button style="margin-left: 20px;" type="primary" :disabled="countdown > 0"
                    @click="sendCode">{{ countdown > 0 ? `重新发送(${countdown}s)` : countdown==-1?'发送验证码':'重新发送' }}</el-button>
                </el-form-item>
              </el-col>

              <el-col :span="8">
                <el-form-item label="登录密码" prop="pwd" :rules="[nonull]">
                  <el-input v-model="inputForm.pwd" placeholder="请输入密码" type="password" show-password style="width: 300px;"></el-input>
                </el-form-item>
              </el-col>

              <el-col :span="8">
                <el-form-item label="企业名称" prop="merchantName" :rules="[nonull]">
                  <el-input v-model="inputForm.merchantName" placeholder="请输入企业名称" style="width: 300px;"></el-input>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="联系人姓名" prop="contactPerson" :rules="[nonull]">
                  <el-input v-model="inputForm.contactPerson" placeholder="请输入类型名称" style="width: 300px;"></el-input>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="联系电话" prop="contactPhone" :rules="[nonull]">
                  <el-input v-model="inputForm.contactPhone" placeholder="请输入类型名称" style="width: 300px;"></el-input>
                </el-form-item>
              </el-col>

              <el-col :span="8">
                <el-form-item label="统一社会信用代码" prop="businessLicense" :rules="[nonull]">
                  <el-input v-model="inputForm.businessLicense" placeholder="请输入类型名称" style="width: 300px;"></el-input>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="法人" prop="legalPerson" :rules="[nonull]">
                  <el-input v-model="inputForm.legalPerson" placeholder="请输入类型名称" style="width: 300px;"></el-input>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="法人身份证号" prop="idCardNumber" :rules="[nonull]">
                  <el-input v-model="inputForm.idCardNumber" placeholder="请输入类型名称" style="width: 300px;"></el-input>
                </el-form-item>
              </el-col>

              <el-col :span="8">
                <el-form-item label="营业执照" prop="licenseImage" :rules="[nonull]">
                  <el-upload accept="image/*" :action="uploadUrl" list-type="picture-card" :show-file-list="false" :on-success="handleSuccess1"
                    :on-remove="handleRemove1" :on-progress="handleProgress1">
                    <el-progress v-if="percentage1 >= 0" type="dashboard" :percentage="percentage1" :color="colors" />
                    <img v-else-if="inputForm.licenseImage" :src="inputForm.licenseImage" style="max-width: 100%;max-height: 100%;border-radius: 6px;" />
                    <el-icon v-else>
                      <Plus />
                    </el-icon>
                  </el-upload>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="法人身份证 - 正面" prop="idCardFront" :rules="[nonull]">
                  <el-upload accept="image/*" :action="uploadUrl" list-type="picture-card" :show-file-list="false" :on-success="handleSuccess2"
                    :on-remove="handleRemove2" :on-progress="handleProgress2">
                    <el-progress v-if="percentage2 >= 0" type="dashboard" :percentage="percentage2" :color="colors" />
                    <img v-else-if="inputForm.idCardFront" :src="inputForm.idCardFront" style="max-width: 100%;max-height: 100%;border-radius: 6px;" />
                    <el-icon v-else>
                      <Plus />
                    </el-icon>
                  </el-upload>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="法人身份证 - 背面" prop="idCardBack" :rules="[nonull]">
                  <el-upload accept="image/*" :action="uploadUrl" list-type="picture-card" :show-file-list="false" :on-success="handleSuccess3"
                    :on-remove="handleRemove3" :on-progress="handleProgress3">
                    <el-progress v-if="percentage3 >= 0" type="dashboard" :percentage="percentage3" :color="colors" />
                    <img v-else-if="inputForm.idCardBack" :src="inputForm.idCardBack" style="max-width: 100%;max-height: 100%;border-radius: 6px;" />
                    <el-icon v-else>
                      <Plus />
                    </el-icon>
                  </el-upload>
                </el-form-item>
              </el-col>

              <el-col :span="24">
                <el-form-item label="详细地址" prop="detailedAddress" :rules="[nonull]">
                  <el-input v-model="inputForm.detailedAddress" :rows="2" type="textarea" show-word-limit maxlength="100" placeholder="请输入详细地址"></el-input>
                </el-form-item>
              </el-col>

              <el-col :span="24">
                <el-form-item label="申请说明" prop="remark" :rules="[nonull]">
                  <el-input v-model="inputForm.remark" :rows="2" type="textarea" show-word-limit maxlength="100" placeholder="请输入申请说明"></el-input>
                </el-form-item>
              </el-col>

            </el-row>

          </el-form>

          <div style="text-align: center;">
            <el-button size="large" type="success" :loading="loading" @click="doSubmit">提交申请</el-button>
          </div>

        </el-tab-pane>
        <el-tab-pane label="进度查询">
          <el-form ref="searchForm" :model="searchForm" label-width="80px" v-loading="loading">
            <el-row>
              <el-col :span="12">
                <el-form-item label="审核编号" prop="auditId" :rules="[nonull]">
                  <el-input v-model="searchForm.auditId" style="width:350px;" clearable />
                  <el-button style="margin-left: 20px;" type="success" icon="search" :loading="loading" @click="searchAudit">查询</el-button>
                </el-form-item>
              </el-col>
            </el-row>
            <div v-if="auditResult" style="font-size: 14px;color: #333;padding: 20px;">
              <span style="color: #666;">查询结果：</span>{{ auditResult }}
            </div>
          </el-form>
        </el-tab-pane>
      </el-tabs>

      <template #footer>
        <span class="dialog-footer">
          <el-button size="large" @click="visible = false">关闭</el-button>
        </span>
      </template>

    </el-dialog>
  </div>
</template>

<script>
import SmsApi from '@/api/SmsApi';
import MerchantInfoApi from '@/api/MerchantInfoApi';
import { mapGetters } from 'vuex';
import { isvalidPhone } from '@/utils/validate';
import FileUploadApi from '@/api/FileUploadApi';

var timer = null;

export default {
  data() {
    return {
      uploadUrl: FileUploadApi.merchantRegFileUpload(),
      nonull: { required: true, message: '必填' },
      visible: false,
      loading: false,
      inputForm: {
        mobile: '',
        smsCode: '',
        pwd: '',
        merchantName: '',
        contactPerson: '',
        contactPhone: '',
        businessLicense: '',
        legalPerson: '',
        idCardNumber: '',
        licenseImage: '',
        idCardFront: '',
        idCardBack: '',
        detailedAddress: '',
        remark: ''
      },
      countdown: -1,
      searchForm: {
        auditId: ''
      },
      auditResult: '',
      colors: [
        { color: '#f56c6c', percentage: 20 },
        { color: '#e6a23c', percentage: 40 },
        { color: '#5cb87a', percentage: 60 },
        { color: '#1989fa', percentage: 80 },
        { color: '#6f7ad3', percentage: 100 }
      ],
      percentage1: -1,
      percentage2: -1,
      percentage3: -1
    };
  },
  components: {},
  computed: {
    ...mapGetters('app', [])
  },
  created() {},
  methods: {
    searchAudit() {
      this.auditResult = '';
      this.$refs['searchForm'].validate((valid) => {
        if (valid) {
          this.loading = true;
          MerchantInfoApi.merchantSearchAudit(this.searchForm.auditId)
            .then((data) => {
              this.auditResult = data;
              this.loading = false;
            })
            .catch(() => {
              this.loading = false;
            });
        }
      });
    },
    sendCode() {
      if (!this.inputForm.mobile || !isvalidPhone(this.inputForm.mobile)) {
        this.$message.error('请输入正确的手机号码');
        return;
      }
      SmsApi.sendCaptcha(this.inputForm.mobile, 'MerchantRegMobile').then((data) => {
        this.inputForm.smsCode = data;
        // 启动倒计时计时器
        this.countdown = 120;
        if (timer) {
          clearInterval(timer);
        }
        timer = setInterval(() => {
          if (this.countdown <= 0) {
            clearInterval(timer);
            return;
          }
          this.countdown--;
        }, 1000);
      });
    },
    doSendCode() {},
    handleRemove1() {
      this.inputForm.licenseImage = '';
    },
    handleSuccess1(response) {
      this.inputForm.licenseImage = response.path;
      this.percentage1 = -1;
    },
    handleProgress1(e) {
      this.percentage1 = Math.floor((e.percent * 100) / 100);
    },
    handleRemove2() {
      this.inputForm.idCardFront = '';
    },
    handleSuccess2(response) {
      this.inputForm.idCardFront = response.path;
      this.percentage2 = -1;
    },
    handleProgress2(e) {
      this.percentage2 = Math.floor((e.percent * 100) / 100);
    },
    handleRemove3() {
      this.inputForm.idCardBack = '';
    },
    handleSuccess3(response) {
      this.inputForm.idCardBack = response.path;
      this.percentage3 = -1;
    },
    handleProgress3(e) {
      this.percentage3 = Math.floor((e.percent * 100) / 100);
    },
    init() {
      this.visible = true;
      this.loading = false;
      this.$nextTick(() => {
        this.$refs.inputForm.resetFields();
        if (timer) {
          clearInterval(timer);
        }
        this.countdown = -1;
      });
    },
    // 表单提交
    doSubmit() {
      this.$refs['inputForm'].validate((valid) => {
        if (valid) {
          this.loading = true;
          MerchantInfoApi.merchantApply(this.inputForm)
            .then((data) => {
              this.$message.success('操作成功');
              this.loading = false;
              this.$alert(`查询审核进度，请通过审核编号：${data}`, '申请成功', {});
            })
            .catch(() => {
              this.loading = false;
            });
        }
      });
    }
  }
};
</script>