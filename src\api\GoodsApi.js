import request from '@/utils/request';

export default {
  goodsList(pagination, searchForm) {
    return request({
      url: '/merchant/goods/list',
      method: 'post',
      data: {
        ...pagination,
        ...searchForm
      }
    });
  },

  goodsInfo(id) {
    return request({
      url: '/merchant/goods/info',
      method: 'post',
      data: {
        id
      }
    });
  },
  goodsDelete(id) {
    return request({
      url: '/merchant/goods/delete',
      method: 'post',
      data: {
        id
      }
    });
  },
  changeDisplay(id, display) {
    return request({
      url: '/merchant/goods/changeDisplay',
      method: 'post',
      data: {
        id,
        display
      }
    });
  },
  goodsSave(data) {
    if (!data.id) {
      return request({
        url: '/api/goods/add',
        method: 'post',
        data: {
          ...data
        }
      });
    } else {
      return request({
        url: '/api/goods/update',
        method: 'post',
        data: {
          ...data
        }
      });
    }
  }
};
