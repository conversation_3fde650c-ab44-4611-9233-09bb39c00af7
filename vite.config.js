import { resolve } from 'path';
import { defineConfig, loadEnv } from 'vite';
import vue from '@vitejs/plugin-vue';

import vueSetupExtend from 'vite-plugin-vue-setup-extend';

// https://vitejs.dev/config/
export default defineConfig(({ mode }) => {
  const env = loadEnv(mode, process.cwd(), '');
  return {
    base: env.VITE_APP_ENV === 'production' ? '/merchant' : '/',
    resolve: {
      // 配置别名
      alias: {
        '@': resolve(__dirname, './src'),
        '@assets': resolve(__dirname, './src/assets'),
        '@views': resolve(__dirname, './src/views'),
        '@components': resolve(__dirname, './src/components')
      }
    },
    plugins: [vue(), vueSetupExtend()],
    build: {
      rollupOptions: {
        output: {
          entryFileNames: `assets/[hash].js`, // 入口文件名格式
          chunkFileNames: `assets/[hash].js`, // 代码分割时非入口chunk的文件名格式
          assetFileNames: `assets/[hash].[ext]` // 静态资源文件名格式
          // entryFileNames: `assets/[name].[hash].js`, // 入口文件名格式
          // chunkFileNames: `assets/[name].[hash].js`, // 代码分割时非入口chunk的文件名格式
          // assetFileNames: `assets/[name].[hash].[ext]` // 静态资源文件名格式
        }
      }
    },
    server: {
      host: '0.0.0.0',
      port: 5000, // 端口号
      open: false, // 是否自动打开浏览器,
      proxy: {
        '/merchant': {
          target: env.VITE_API_URL,
          changeOrigin: true
          // rewrite: (path) => path.replace(/^\/mall\/api/, '/')
        }
      }
    },
    css: {
      loaderOptions: {
        scss: {
          additionalData: `@import "@assets/styles/variables.module.scss";`
        }
      },
      preprocessorOptions: {
        scss: {
          api: 'modern-compiler'
        }
      }
    }
  };
});
