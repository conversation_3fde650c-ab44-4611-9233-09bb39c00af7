<template>
  <div>
    <el-dialog v-model="visible" title="结算详情" width="1200px" style="margin-top: 50px;" :close-on-click-modal="false" draggable>
      <el-form v-loading="loading" label-width="120px" label-position="top">
        <el-tabs v-model="activeName" @tab-change="onTabChange">

          <el-tab-pane v-if="info.settleId" label="结算信息" :name="0">

            <el-row :gutter="15">
              <el-col :span="6">
                <el-form-item label="结算单号">
                  <div class="info-value">{{ info.settleId }}</div>
                </el-form-item>
              </el-col>
              <el-col :span="6">
                <el-form-item label="商户">
                  <div class="info-value">{{ info.merchantName }}</div>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="结算周期">
                  <div class="info-value">{{ info.startTime.substring(0, 10) }} ~ {{ info.endTime.substring(0, 10) }}</div>
                </el-form-item>
              </el-col>

              <el-col :span="6">
                <el-form-item label="银行名称">
                  <div class="info-value">{{ info.bankName }}</div>
                </el-form-item>
              </el-col>

              <el-col :span="6">
                <el-form-item label="银行账户">
                  <div class="info-value">{{ info.bankAccount }}</div>
                </el-form-item>
              </el-col>
              <el-col :span="6">
                <el-form-item label="开户人姓名">
                  <div class="info-value">{{ info.bankAccountHolder }}</div>
                </el-form-item>
              </el-col>

              <el-col :span="6">
                <el-form-item label="结算状态">
                  <div class="info-value">
                    <el-tag v-if="info.settleStatus === 0" type="warning">待结算</el-tag>
                    <el-tag v-else-if="info.settleStatus === 1" type="primary">商户已确认</el-tag>
                    <el-tag v-else-if="info.settleStatus === 2" type="primary">平台已确认</el-tag>
                    <el-tag v-else-if="info.settleStatus === 10" type="success">已完成</el-tag>
                  </div>
                </el-form-item>
              </el-col>

              <el-col :span="6">
                <el-form-item label="总入账">
                  <div class="info-value" style="color: red;">￥ {{ info.income }}</div>
                </el-form-item>
              </el-col>
              <el-col :span="6">
                <el-form-item label="总出账">
                  <div class="info-value">￥ {{ info.expenses }}</div>
                </el-form-item>
              </el-col>
              <el-col :span="6">
                <el-form-item label="结算佣金">
                  <div class="info-value" style="color: orange;">￥ {{ info.settleCommission }}</div>
                </el-form-item>
              </el-col>
              <el-col :span="6">
                <el-form-item label="结算金额">
                  <div class="info-value" style="color: green;">￥ {{ info.total }}</div>
                </el-form-item>
              </el-col>

            </el-row>
          </el-tab-pane>

          <el-tab-pane label="明细" :name="1">
            <el-table :data="tableData" border style="margin-top: 10px; ">
              <el-table-column type="index" label="#" width="55" align="center" />
              <el-table-column prop="orderCode" label="订单号" width="230">
                <template #default="scope">
                  <el-link type="primary" underline="never" @click="toDetail(scope.row)">{{ scope.row.orderCode }}</el-link>
                </template>
              </el-table-column>
              <el-table-column prop="flowAmount" label="入账" width="120">
                <template #default="scope">
                  ￥ {{ scope.row.flowAmount }}
                </template>
              </el-table-column>
              <el-table-column prop="commission" label="佣金比例" width="120">
                <template #default="scope">
                  {{ scope.row.commission }} %
                </template>
              </el-table-column>
              <el-table-column prop="settleCommission" label="佣金" width="120">
                <template #default="scope">
                  ￥ {{ scope.row.settleCommission }}
                </template>
              </el-table-column>
              <el-table-column prop="settleAmount" label="结算金额" width="120">
                <template #default="scope">
                  ￥ {{ scope.row.settleAmount }}
                </template>
              </el-table-column>
            </el-table>
            <el-pagination style="margin-top: 10px;" ref="pagination" :total="pagination.total" v-model:current-page="pagination.current"
              v-model:page-size="pagination.size" :page-sizes="[10, 20, 50]" @size-change="handlePageSizeChange" @current-change="handlePageCurrentChange"
              layout="prev, pager, next, total, sizes">
            </el-pagination>

          </el-tab-pane>
        </el-tabs>

      </el-form>

      <el-divider />

      <template #footer>
        <span class="dialog-footer">
          <el-button size="large" @click="visible = false">关闭</el-button>
        </span>
      </template>

    </el-dialog>

    <OrderDetails ref="orderDetails"></OrderDetails>
  </div>
</template>

<script>
import SettleApi from '@/api/SettleApi';
import Config from '@/settings';
import { mapGetters } from 'vuex';
import OrderDetails from '@/views/order/OrderDetails.vue';

export default {
  props: {},
  data() {
    return {
      activeName: 0,
      nonull: { required: true, message: '必填' },
      visible: false,
      loading: false,
      info: {},
      tableData: [],
      pagination: {
        total: 0,
        size: Config.pageSize,
        current: 1
      }
    };
  },
  components: { OrderDetails },
  computed: {
    ...mapGetters('app', [])
  },
  created() {},
  mounted() {},
  methods: {
    toDetail(row) {
      this.$refs.orderDetails.init(row.orderCode);
    },
    onTabChange(val) {
      if (val == 1) {
        this.refreshList();
      }
    },
    init(settleId) {
      this.activeName = 0;
      this.info = {};
      this.tableData = [];
      this.visible = true;
      this.loading = false;
      this.$nextTick(() => {
        this.loading = true;
        SettleApi.settleInfo(settleId).then((data) => {
          this.info = data;
          this.loading = false;
        });
      });
    },
    refreshList() {
      this.loading = true;
      SettleApi.settleRecordsList(this.pagination, { settleId: this.info.settleId }).then((data) => {
        this.tableData = data.records;
        this.pagination.total = data.total;
        // 删除后如果当前页没数据,则返回前一页
        if (data.records.length === 0 && data.total > 0 && this.pagination.current > 1) {
          this.pagination.current = this.pagination.current - 1;
          this.refreshList();
        }
        this.loading = false;
      });
    },
    handlePageSizeChange() {
      this.pagination.current = 1;
      this.refreshList();
    },
    handlePageCurrentChange() {
      this.refreshList();
    }
  }
};
</script>

<style lang="scss" scoped>
.info-value {
  font-size: 16px;
  font-weight: bold;
  color: #333;
}

.info-img {
  border-radius: 6pxs;
  border: 1px solid #ddd;
  width: 150px;
  height: 150px;
  margin-right: 20px;
}
</style>