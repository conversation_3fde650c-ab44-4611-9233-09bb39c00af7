<template>
  <div>
    <el-form ref="searchForm" :model="searchForm" label-width="80px">
      <el-row>
        <el-col :span="6">
          <el-form-item label="订单号" prop="orderCode">
            <el-input v-model="searchForm.orderCode" style="width:200px;" clearable />
          </el-form-item>
        </el-col>
        <!-- <el-col :span="6">
          <el-form-item label="商品类型" prop="serviceGoods">
            <el-select v-model="searchForm.serviceGoods" placeholder="请选择商品类型">
              <el-option label="服务类" value="1"></el-option>
              <el-option label="实物商品" value="2"></el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="商品分类" prop="goodsTypeId">
            <el-select v-model="searchForm.goodsTypeId" placeholder="请选择商品分类">
              <el-option v-for="item in goodsTypeList" :key="item.id" :label="item.name" :value="item.id"></el-option>
            </el-select>
          </el-form-item>
        </el-col> -->
        <el-col :span="6">
          <el-form-item>
            <el-button type="success" icon="search" @click="search">查询</el-button>
            <el-button type="warning" icon="refresh-left" @click="reset">重置</el-button>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>

    <el-table :data="tableData" border style="margin-top: 10px; " v-loading="loading">
      <el-table-column type="index" label="#" width="55" align="center" />
      <el-table-column prop="orderCode" label="订单号" width="230">
        <template #default="scope">
          <el-link type="primary" underline="never" @click="toDetail(scope.row)">{{ scope.row.orderCode }}</el-link>
        </template>
      </el-table-column>
      <el-table-column prop="user.code" label="买家" width="130"></el-table-column>
      <el-table-column prop="orderDelivery.userName" label="联系人" width="130"></el-table-column>
      <el-table-column prop="orderDelivery.telNumber" label="联系电话" width="130"></el-table-column>
      <el-table-column prop="orderPayPrice" label="订单金额" width="130">
        <template #default="scope">
          ￥ {{ scope.row.orderPayPrice.toFixed(2) }}
        </template>
      </el-table-column>

      <el-table-column prop="orderStatus" label="订单状态" width="130">
        <template #default="scope">
          <el-tag v-if="scope.row.orderStatus === 0" type="warning">待支付</el-tag>
          <el-tag v-else-if="scope.row.orderStatus === 1" type="info">用户已取消</el-tag>
          <el-tag v-else-if="scope.row.orderStatus === 11" type="primary">待发货</el-tag>
          <el-tag v-else-if="scope.row.orderStatus === 12" type="info">用户已取消</el-tag>
          <el-tag v-else-if="scope.row.orderStatus === 21" type="primary">已发货/进行中</el-tag>
          <el-tag v-else-if="scope.row.orderStatus === 50" type="success">已完成</el-tag>

        </template>
      </el-table-column>
      <el-table-column prop="payStatus" label="支付状态" width="130">
        <template #default="scope">
          <el-tag v-if="scope.row.payStatus === 0" type="warning">待支付</el-tag>
          <el-tag v-else-if="scope.row.payStatus === 1" type="success">已付款</el-tag>
          <el-tag v-else-if="scope.row.payStatus === 2" type="info">已退款</el-tag>

        </template>
      </el-table-column>
      <el-table-column prop="createTime" label="下单时间" width="140">
        <template #default="scope">
          {{ scope.row.createTime.substring(0, 16) }}
        </template>
      </el-table-column>

    </el-table>
    <el-pagination style="margin-top: 10px;" ref="pagination" :total="pagination.total" v-model:current-page="pagination.current"
      v-model:page-size="pagination.size" :page-sizes="[10, 20, 50]" @size-change="handlePageSizeChange"
      @current-change="handlePageCurrentChange" layout="prev, pager, next, total, sizes">
    </el-pagination>

    <OrderDetails ref="orderDetails" @refreshDataList="refreshList"></OrderDetails>
  </div>
</template>

<script>
import OrderDetails from '@/views/order/OrderDetails.vue';
import Config from '@/settings';
import MerchantUserApi from '@/api/MerchantUserApi';

export default {
  name: 'CustomerOrderList',
  props: {
    userId: { type: String, default: '' }
  },
  data() {
    return {
      tableData: [],
      searchForm: {
        userId: ''
      },
      pagination: {
        total: 0,
        size: Config.pageSize,
        current: 1
      },
      loading: false
    };
  },
  components: { OrderDetails },
  mounted() {
    this.searchForm.userId = this.userId;
    this.refreshList();
  },
  methods: {
    toDetail(row) {
      this.$refs.orderDetails.init(row.orderCode);
    },
    search() {
      this.pagination.current = 1;
      this.refreshList();
    },
    reset() {
      this.$refs.searchForm.resetFields();
      this.pagination.current = 1;
      this.refreshList();
    },
    refreshList() {
      this.loading = true;
      MerchantUserApi.merchantCustomeOrderList(this.pagination, this.searchForm).then((data) => {
        this.tableData = data.records;
        this.pagination.total = data.total;
        // 删除后如果当前页没数据,则返回前一页
        if (data.records.length === 0 && data.total > 0 && this.pagination.current > 1) {
          this.pagination.current = this.pagination.current - 1;
          this.refreshList();
        }
        this.loading = false;
      });
    },
    handlePageSizeChange() {
      this.pagination.current = 1;
      this.refreshList();
    },
    handlePageCurrentChange() {
      this.refreshList();
    }
  }
};
</script>

<style lang="scss" scoped>
</style>