<template>
  <router-view v-slot="{ Component }">
    <transition mode="out-in" name="fade-transform">
      <keep-alive :include="cache">
        <component :is="Component"></component>
      </keep-alive>
    </transition>
  </router-view>
</template>

<script>
import { mapGetters } from 'vuex';

export default {
  name: 'AppMain',
  computed: {
    ...mapGetters('tags', ['cachedViews']),
    cache() {
      return this.cachedViews.filter((e) => e.keepAlive).map((e) => e.name);
    },
    key() {
      return this.$route.path;
    }
  }
};
</script>

<style lang="scss" scoped>
.fade-transform-leave-active {
  transition: all 0.5s;
}
.fade-transform-enter-active {
  transition: all 0.5s;
}

.fade-transform-enter-from {
  opacity: 0;
  transform: translateX(-30px);
}

.fade-transform-leave-to {
  opacity: 0;
  transform: translateX(30px);
}
</style>