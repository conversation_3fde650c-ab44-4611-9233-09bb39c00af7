{"name": "mallweb", "private": true, "version": "0.1.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview"}, "dependencies": {"@element-plus/icons-vue": "^2.3.1", "@fortawesome/fontawesome-svg-core": "^6.7.2", "@fortawesome/free-brands-svg-icons": "^6.7.2", "@fortawesome/free-regular-svg-icons": "^6.7.2", "@fortawesome/free-solid-svg-icons": "^6.7.2", "@fortawesome/vue-fontawesome": "^3.0.8", "@vueup/vue-quill": "^1.2.0", "axios": "^1.9.0", "echarts": "^5.6.0", "element-plus": "^2.9.9", "mitt": "^3.0.1", "moment": "^2.30.1", "vue": "^3.5.13", "vue-router": "^4.5.1", "vuex": "^4.1.0", "vuex-persistedstate": "^4.1.0", "vxe-table": "^4.13.17", "xe-utils": "^3.7.4"}, "devDependencies": {"@vitejs/plugin-vue": "^5.2.3", "eslint": "^8.57.0", "eslint-plugin-vue": "^9.27.0", "prettier": "^3.5.3", "sass": "^1.87.0", "vite": "^6.3.5", "vite-plugin-vue-setup-extend": "^0.4.0"}}