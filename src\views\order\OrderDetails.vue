<template>
  <div>
    <el-dialog v-model="visible" title="订单详情" width="1200px" style="margin-top: 50px;" :close-on-click-modal="false" draggable>
      <el-form ref="inputForm" :model="inputForm" v-loading="loading" label-width="120px" label-position="top">
        <el-tabs v-model="activeName">

          <el-tab-pane v-if="inputForm.user" label="订单信息" :name="0">

            <el-row :gutter="15">
              <el-col :span="6">
                <el-form-item label="订单号" prop="orderCode">
                  <div class="info-value">{{ inputForm.orderCode }}</div>
                </el-form-item>
              </el-col>
              <el-col :span="6">
                <el-form-item label="买家" prop="user.code">
                  <div class="info-value">{{ inputForm.user.code }}</div>
                </el-form-item>
              </el-col>
              <el-col :span="6">
                <el-form-item label="联系人" prop="orderDelivery.userName">
                  <div class="info-value">{{ inputForm.orderDelivery.userName }}</div>
                </el-form-item>
              </el-col>

              <el-col :span="6">
                <el-form-item label="联系电话" prop="orderDelivery.telNumber">
                  <div class="info-value">{{ inputForm.orderDelivery.telNumber }}</div>
                </el-form-item>
              </el-col>

              <el-col :span="6">
                <el-form-item label="订单金额" prop="orderPayPrice">
                  <div class="info-value">￥ {{ inputForm.orderPayPrice.toFixed(2) }}</div>
                </el-form-item>
              </el-col>
              <el-col :span="6">
                <el-form-item label="订单状态" prop="inputForm.orderStatus">
                  <div class="info-value">
                    <el-tag v-if="inputForm.orderStatus === 0" type="warning">待支付</el-tag>
                    <el-tag v-else-if="inputForm.orderStatus === 1" type="info">用户已取消</el-tag>
                    <el-tag v-else-if="inputForm.orderStatus === 11" type="primary">待发货</el-tag>
                    <el-tag v-else-if="inputForm.orderStatus === 12" type="info">用户已取消</el-tag>
                    <el-tag v-else-if="inputForm.orderStatus === 21" type="primary">已发货/进行中</el-tag>
                    <el-tag v-else-if="inputForm.orderStatus === 50" type="success">已完成</el-tag>
                  </div>
                </el-form-item>
              </el-col>
              <el-col :span="6">
                <el-form-item label="支付状态" prop="payStatus">
                  <div class="info-value">
                    <el-tag v-if="inputForm.payStatus === 0" type="warning">待支付</el-tag>
                    <el-tag v-else-if="inputForm.payStatus === 1" type="success">已付款</el-tag>
                    <el-tag v-else-if="inputForm.payStatus === 2" type="info">已退款</el-tag>
                  </div>
                </el-form-item>
              </el-col>

              <el-col :span="6">
                <el-form-item label="下单时间" prop="createTime">
                  <div class="info-value">{{ inputForm.createTime }}</div>
                </el-form-item>
              </el-col>

            </el-row>
          </el-tab-pane>
        </el-tabs>

        <el-tabs v-model="activeName">
          <el-tab-pane label="订单商品" :name="0">
            <el-table :data="inputForm.orderItemList" border style="margin-top: 10px; ">
              <el-table-column type="index" label="#" width="55" align="center" />
              <el-table-column prop="pic" label="图片" width="80">
                <template #default="scope">
                  <el-image :z-index="9999" preview-teleported style="width: 50px; height: 50px" :src="scope.row.pic" :preview-src-list="[scope.row.pic]"
                    :initial-index="0" :teleported="true" fit="cover" />
                </template>
              </el-table-column>
              <el-table-column prop="goodsName" label="名称"></el-table-column>
              <el-table-column prop="buyNum" label="购买数量" width="100"></el-table-column>
              <el-table-column prop="specDesc" label="规格"></el-table-column>
              <el-table-column prop="price" label="价格" width="130"></el-table-column>
            </el-table>

          </el-tab-pane>
        </el-tabs>

        <el-tabs v-if="inputForm.orderDelivery" v-model="activeName">
          <el-tab-pane label="配送信息" :name="0">
            <el-row>
              <el-col :span="6">
                <el-form-item label="联系人" prop="orderDelivery.userName">
                  <div class="info-value">{{ inputForm.orderDelivery.userName }}</div>
                </el-form-item>
              </el-col>

              <el-col :span="6">
                <el-form-item label="联系电话" prop="orderDelivery.telNumber">
                  <div class="info-value">{{ inputForm.orderDelivery.telNumber }}</div>
                </el-form-item>
              </el-col>

              <el-col :span="12">
                <el-form-item label="地址">
                  <div class="info-value">
                    {{ inputForm.orderDelivery.provinceName + inputForm.orderDelivery.cityName + inputForm.orderDelivery.countyName + inputForm.orderDelivery.detailInfo }}
                  </div>
                </el-form-item>
              </el-col>

            </el-row>
            <el-row v-if="inputForm.orderDelivery.status > 1">
              <el-col :span="6">
                <el-form-item label="配送方式" prop="orderDelivery.deliveryMethod">
                  <div v-if="inputForm.orderDelivery.deliveryMethod==2" class="info-value">线下服务</div>
                  <div v-else-if="inputForm.orderDelivery.deliveryMethod==3" class="info-value">快递物流</div>
                </el-form-item>
              </el-col>

              <el-col :span="6" v-if="inputForm.orderDelivery.deliveryMethod==3">
                <el-form-item label="快递单号" prop="orderDelivery.deliveryCode">
                  <div class="info-value">{{ inputForm.orderDelivery.deliveryCode }}</div>
                </el-form-item>
              </el-col>

              <el-col :span="12" v-if="inputForm.orderDelivery.deliveryMethod==3">
                <el-form-item label="快递公司">
                  <div class="info-value">
                    {{ inputForm.orderDelivery.shipper }}
                  </div>
                </el-form-item>
              </el-col>

            </el-row>

          </el-tab-pane>

        </el-tabs>
      </el-form>

      <el-divider />

      <div style="text-align: center;">
        <el-button v-if="inputForm.orderStatus === 11" size="large" type="primary" @click="toSendDelivery">去发货</el-button>
      </div>

      <template #footer>
        <span class="dialog-footer">
          <el-button size="large" @click="visible = false">关闭</el-button>
        </span>
      </template>

      <SendDelivery ref="sendDelivery" @refreshDataList="init"></SendDelivery>

    </el-dialog>
  </div>
</template>

<script>
import SendDelivery from './SendDelivery.vue';
import OrderApi from '@/api/OrderApi';
import { mapGetters } from 'vuex';

export default {
  props: {},
  data() {
    return {
      activeName: 0,
      title: '',
      nonull: { required: true, message: '必填' },
      visible: false,
      loading: false,
      inputForm: {
        orderCode: '',
        orderPayPrice: '',
        orderStatus: '',
        payStatus: '',
        remark: '',
        createTime: '',
        modifiedTime: '',
        orderItemList: [],
        orderDelivery: undefined,
        user: undefined
      }
    };
  },
  components: { SendDelivery },
  computed: {
    ...mapGetters('app', [])
  },
  created() {},
  mounted() {},
  methods: {
    toSendDelivery() {
      this.$refs.sendDelivery.init(this.inputForm.orderCode);
    },
    init(orderCode) {
      this.inputForm.orderCode = orderCode;
      this.visible = true;
      this.loading = false;
      this.$nextTick(() => {
        this.$refs.inputForm.resetFields();
        this.loading = true;
        OrderApi.orderInfo(this.inputForm.orderCode).then((data) => {
          this.inputForm = this.$utils.recover(this.inputForm, data);
          this.loading = false;
        });
      });
    }
  }
};
</script>

<style lang="scss" scoped>
.info-value {
  font-size: 16px;
  font-weight: bold;
  color: #333;
}

.info-img {
  border-radius: 6pxs;
  border: 1px solid #ddd;
  width: 150px;
  height: 150px;
  margin-right: 20px;
}
</style>