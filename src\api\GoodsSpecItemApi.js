import request from '@/utils/request';

export default {
  goodsSpecItemList(searchForm) {
    return request({
      url: '/merchant/goodsSpecItem/list',
      method: 'post',
      data: {
        ...searchForm
      }
    });
  },

  goodsSpecItemInfo(id) {
    return request({
      url: '/merchant/goodsSpecItem/info',
      method: 'post',
      data: {
        id
      }
    });
  },

  goodsSpecItemDelete(id) {
    return request({
      url: '/merchant/goodsSpecItem/delete',
      method: 'post',
      data: {
        id
      }
    });
  },

  goodsSpecItemSave(data) {
    if (!data.id) {
      return request({
        url: '/merchant/goodsSpecItem/add',
        method: 'post',
        data: {
          ...data
        }
      });
    } else {
      return request({
        url: '/merchant/goodsSpecItem/update',
        method: 'post',
        data: {
          ...data
        }
      });
    }
  }
};
