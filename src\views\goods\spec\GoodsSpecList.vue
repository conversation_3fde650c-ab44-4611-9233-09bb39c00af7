<template>
  <div class="app-container">
    <el-form ref="searchForm" :model="searchForm" label-width="80px">
      <el-row>
        <el-col :span="6">
          <el-form-item label="规格名称" prop="name">
            <el-input v-model="searchForm.name" style="width:200px;" clearable />
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item>
            <el-button type="success" icon="search" @click="search">查询</el-button>
            <el-button type="warning" icon="refresh-left" @click="reset">重置</el-button>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <el-row>
      <el-button type="primary" icon="plus" @click="toAdd">新增</el-button>
    </el-row>
    <el-table ref="table" :data="tableData" border style="margin-top: 10px;" highlight-current-row @current-change="handleCurrentChange" v-loading="loading">
      <el-table-column type="index" label="#" width="55" align="center" />
      <el-table-column prop="name" label="规格名称"></el-table-column>
      <el-table-column prop="showName" label="显示名称"></el-table-column>
      <el-table-column prop="remark" label="描述"></el-table-column>
      <el-table-column prop="createTime" label="创建时间" width="200"></el-table-column>
      <el-table-column label="操作" width="290">
        <template #default="scope">
          <el-button type="primary" link icon="edit" @click="handleCurrentChange(scope.row)">配置子规格</el-button>
          <el-button type="primary" link icon="edit" @click="toEdit(scope.row)">修改</el-button>
          <el-button type="danger" link icon="delete" @click="toDel(scope.row)">删除</el-button>

        </template>
      </el-table-column>

    </el-table>

    <el-pagination style="margin-top: 10px;" ref="pagination" :total="pagination.total" v-model:current-page="pagination.current"
      v-model:page-size="pagination.size" :page-sizes="[10, 20, 50]" @size-change="handlePageSizeChange" @current-change="handlePageCurrentChange"
      layout="prev, pager, next, total, sizes">
    </el-pagination>

    <el-drawer v-model="drawer" title="规格项" direction="rtl" :close-on-click-modal="false" :close-on-press-escape="false" :before-close="onDrawerClose">
      <el-row>
        <el-button type="primary" icon="plus" @click="toItemAdd">新增</el-button>
      </el-row>
      <el-table :data="tableDataItem" border style="margin-top: 10px;" v-loading="loading2">
        <el-table-column type="index" label="#" width="55" align="center" />
        <el-table-column prop="name" label="名称"></el-table-column>
        <el-table-column prop="remark" label="描述"></el-table-column>
        <el-table-column label="操作" width="150">
          <template #default="scope">
            <el-button type="primary" link icon="edit" @click="toItemEdit(scope.row)">修改</el-button>
            <el-button type="danger" link icon="delete" @click="toItemDel(scope.row)">删除</el-button>
          </template>
        </el-table-column>

      </el-table>
    </el-drawer>

    <!-- 弹窗, 新增 / 修改 -->
    <GoodsSpecForm ref="goodsSpecForm" @refreshDataList="refreshList"></GoodsSpecForm>
    <GoodsSpecItemForm ref="goodsSpecItemForm" @refreshDataList="refreshItemList"></GoodsSpecItemForm>
  </div>
</template>

<script>
import Config from '@/settings';
import { ElMessageBox } from 'element-plus';
import { h } from 'vue';
import GoodsSpecForm from './GoodsSpecForm.vue';
import GoodsSpecItemForm from './GoodsSpecItemForm.vue';
import GoodsSpecApi from '@/api/GoodsSpecApi';
import GoodsSpecItemApi from '@/api/GoodsSpecItemApi';

export default {
  name: 'GoodsSpecList',
  data() {
    return {
      tableData: [],
      searchForm: {
        name: ''
      },
      pagination: {
        current: 1,
        size: Config.pageSize,
        total: 0
      },
      loading: false,
      selectItem: {},
      drawer: false,
      tableDataItem: [],
      loading2: false
    };
  },
  components: {
    GoodsSpecForm,
    GoodsSpecItemForm
  },
  activated() {
    this.refreshList();
  },
  methods: {
    handleCurrentChange(row) {
      if (row) {
        this.drawer = true;
        this.selectItem = row;
        this.refreshItemList();
      }
    },
    toItemAdd() {
      this.$refs.goodsSpecItemForm.init('add', this.selectItem.id, '');
    },
    toItemEdit(row) {
      this.$refs.goodsSpecItemForm.init('edit', this.selectItem.id, row.id);
    },
    toItemDel(row) {
      ElMessageBox.confirm(h('p', null, [h('span', null, '确认删除 '), h('i', { style: 'color: red' }, row.name)]), '警告', {
        confirmButtonText: '确认',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          GoodsSpecItemApi.goodsSpecItemDelete(row.id).then(() => {
            this.$message.success('删除成功');
            this.refreshItemList();
          });
        })
        .catch(() => {});
    },
    refreshItemList() {
      if (this.selectItem.id) {
        this.loading2 = true;
        this.tableDataItem = [];
        GoodsSpecItemApi.goodsSpecItemList({ goodsSpecId: this.selectItem.id }).then((data) => {
          this.tableDataItem = data;
          this.loading2 = false;
        });
      }
    },
    onDrawerClose(done) {
      this.$refs.table.setCurrentRow();
      this.selectItem = {};
      done();
    },
    toAdd() {
      this.$refs.goodsSpecForm.init('add', '');
    },
    toEdit(row) {
      this.$refs.goodsSpecForm.init('edit', row.id);
    },
    toDel(row) {
      ElMessageBox.confirm(h('p', null, [h('span', null, '确认删除 '), h('i', { style: 'color: red' }, row.name)]), '警告', {
        confirmButtonText: '确认',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          GoodsSpecApi.goodsSpecDelete(row.id).then(() => {
            this.$message.success('删除成功');
            this.refreshList();
          });
        })
        .catch(() => {});
    },
    search() {
      this.pagination.current = 1;
      this.refreshList();
    },
    reset() {
      this.$refs.searchForm.resetFields();
      this.pagination.current = 1;
      this.refreshList();
    },
    refreshList() {
      this.loading = true;
      GoodsSpecApi.goodsSpecList(this.pagination, this.searchForm).then((data) => {
        this.tableData = data.records;
        this.pagination.total = data.total;
        // 删除后如果当前页没数据,则返回前一页
        if (data.records.length === 0 && data.total > 0 && this.pagination.current > 1) {
          this.pagination.current = this.pagination.current - 1;
          this.refreshList();
        }
        this.loading = false;
      });
    },
    handlePageSizeChange() {
      this.pagination.current = 1;
      this.refreshList();
    },
    handlePageCurrentChange() {
      this.refreshList();
    }
  }
};
</script>

<style lang="scss" scoped></style>