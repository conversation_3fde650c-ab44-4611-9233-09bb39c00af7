<template>
  <div class="login-wrap">
    <div style="display: flex;flex-direction: column;align-items: center;">
      <img class="login-logo" src="~@/assets/images/login_logo.png" />
    </div>
    <el-form ref="loginForm" :model="loginForm" :rules="loginRules" label-position="left" label-width="0px" class="login-form">
      <h3 class="login-title">商户管理系统</h3>

      <el-form-item prop="userCode">
        <el-input v-model="loginForm.userCode" type="text" auto-complete="off" placeholder="账号">
        </el-input>
      </el-form-item>
      <el-form-item prop="userPwd">
        <el-input v-model="loginForm.userPwd" type="password" auto-complete="off" placeholder="密码" @keyup.enter="handleLogin">
        </el-input>
      </el-form-item>
      <el-form-item prop="verifyCode">
        <el-input v-model="loginForm.verifyCode" auto-complete="off" placeholder="验证码" style="width: 63%" @keyup.enter="handleLogin"> </el-input>
        <div class="login-code">
          <img :src="verifyCodeUrl" @click="getVerifyCode" />
        </div>
      </el-form-item>
      <el-form-item>
        <div style="width: 100%;;display: flex;flex-direction: row;justify-content: space-around;">
          <el-button :loading="loading" size="large" type="primary" style="width:100%;" @click.prevent="handleLogin">
            <span v-if="!loading">登 录</span>
            <span v-else>登 录 中...</span>
          </el-button>
        </div>
        <el-link style="margin: 20px auto 0;" type="primary" underline="never" @click.prevent="showFormDialog">申请入驻</el-link>
      </el-form-item>

    </el-form>

    <!-- <div v-if="$store.state.settings.showFooter" id="el-login-footer">
      <span v-html="$store.state.settings.footerTxt"></span>
      <span v-if="$store.state.settings.caseNumber"> ⋅ </span>
      <a href="https://beian.miit.gov.cn/#/Integrated/index" target="_blank">{{ $store.state.settings.caseNumber }}</a>
    </div> -->

    <RegForm ref="regForm" />

  </div>
</template>

<script>
import LoginApi from '@/api/LoginApi';
import { ElMessage } from 'element-plus';
import RegForm from './RegForm.vue';

export default {
  data() {
    return {
      nonull: { required: true, message: '必填' },
      verifyCodeUrl: '',
      cookiePass: '',
      loginForm: {
        userCode: '',
        userPwd: '',
        verifyCode: '',
        verifyUuid: ''
      },
      loginRules: {
        userCode: [{ required: true, trigger: 'blur', message: '用户名不能为空' }],
        userPwd: [{ required: true, trigger: 'blur', message: '密码不能为空' }],
        verifyCode: [{ required: true, trigger: 'change', message: '验证码不能为空' }]
      },
      loading: false,
      redirect: undefined
    };
  },
  components: { RegForm },
  created() {
    // 获取验证码
    this.getVerifyCode();
  },
  methods: {
    showFormDialog() {
      this.$refs.regForm.init();
    },
    getVerifyCode() {
      LoginApi.getVerifyCode().then((res) => {
        this.verifyCodeUrl = res.img;
        this.loginForm.verifyUuid = res.uuid;
      });
    },
    handleLogin() {
      this.$refs['loginForm'].validate((valid) => {
        if (valid) {
          this.loading = true;
          LoginApi.login(this.loginForm.userCode, this.loginForm.userPwd, this.loginForm.verifyCode, this.loginForm.verifyUuid)
            .then((response) => {
              this.$store.commit('app/setUser', response.user);
              this.$store.commit('app/setAuthToken', response.token);

              console.info(`[${response.user.code}] login success.`);

              this.$router.replace('/');
            })
            .catch((error) => {
              console.log(error);
              ElMessage.error((error.response.data && error.response.data.msg) || '请求失败');
            })
            .finally(() => {
              // toastLoading.clear();
              this.loading = false;
            });
        }
      });
    }
  }
};
</script>

<style scoped lang="scss">
.login-wrap {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  height: 100%;
  background-size: cover;
  background-image: url('@/assets/images/login_bg.png');

  .login-title {
    margin: 10px auto 20px auto;
    text-align: center;
    color: #707070;
  }

  .login-logo {
    width: 200px;
    text-align: center;
    margin-bottom: 30px;
  }

  .login-form {
    box-shadow: 0px 0px 10px 0px #999999;
    border-radius: 6px;
    background: #ffffff;
    width: 385px;
    padding: 25px 25px 5px 25px;
    margin-bottom: 150px;

    .el-input {
      height: 38px;

      input {
        height: 38px;
      }
    }

    .input-icon {
      height: 39px;
      width: 14px;
      margin-left: 2px;
    }
  }

  .login-tip {
    font-size: 13px;
    text-align: center;
    color: #bfbfbf;
  }

  .login-code {
    width: 33%;
    display: inline-block;
    height: 38px;

    img {
      cursor: pointer;
      vertical-align: middle;
    }
  }
}
</style>
