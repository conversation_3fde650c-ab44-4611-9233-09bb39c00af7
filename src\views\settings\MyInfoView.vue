<template>
  <div style="padding: 20px;">
    <el-form ref="inputForm" :model="inputForm" v-loading="loading" label-width="120px" label-position="top">
      <el-row :gutter="15">

        <el-col :span="6">
          <el-form-item label="手机号" prop="userMobile">
            <div class="info-value">{{ inputForm.userMobile }}</div>
          </el-form-item>
        </el-col>

        <el-col :span="6">
          <el-form-item label="企业名称" prop="merchantName">
            <div class="info-value">{{ inputForm.merchantName }}</div>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="联系人姓名" prop="contactPerson">
            <div class="info-value">{{ inputForm.contactPerson }}</div>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="联系电话" prop="contactPhone">
            <div class="info-value">{{ inputForm.contactPhone }}</div>
          </el-form-item>
        </el-col>

        <el-col :span="6">
          <el-form-item label="统一社会信用代码" prop="businessLicense">
            <div class="info-value">{{ inputForm.businessLicense }}</div>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="法人" prop="legalPerson">
            <div class="info-value">{{ inputForm.legalPerson }}</div>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="法人身份证号" prop="idCardNumber">
            <div class="info-value">{{ inputForm.idCardNumber }}</div>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="详细地址" prop="detailedAddress">
            <div class="info-value">{{ inputForm.detailedAddress }}</div>
          </el-form-item>
        </el-col>

        <el-col :span="8">
          <el-form-item label="营业执照" prop="licenseImage">
            <el-image :z-index="9999" preview-teleported class="info-img" :src="`${inputForm.licenseImage}`" :preview-src-list="[`${inputForm.licenseImage}`]"
              :initial-index="0" :teleported="true" fit="cover" />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="法人身份证 - 正面" prop="idCardFront">
            <el-image :z-index="9999" preview-teleported class="info-img" :src="`${inputForm.idCardFront}`" :preview-src-list="[`${inputForm.idCardFront}`]"
              :initial-index="0" :teleported="true" fit="cover" />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="法人身份证 - 背面" prop="idCardBack">
            <el-image :z-index="9999" preview-teleported class="info-img" :src="`${inputForm.idCardBack}`" :preview-src-list="[`${inputForm.idCardBack}`]"
              :initial-index="0" :teleported="true" fit="cover" />
          </el-form-item>
        </el-col>

      </el-row>

    </el-form>

  </div>
</template>

<script>
import MerchantInfoApi from '@/api/MerchantInfoApi';
import { mapGetters } from 'vuex';

export default {
  name: 'myInfo',
  props: {},
  data() {
    return {
      loading: false,
      inputForm: {
        id: '',
        userMobile: '',
        merchantName: '',
        contactPerson: '',
        contactPhone: '',
        businessLicense: '',
        legalPerson: '',
        idCardNumber: '',
        licenseImage: '',
        idCardFront: '',
        idCardBack: '',
        detailedAddress: '',
        remark: ''
      }
    };
  },
  components: {},
  computed: {
    ...mapGetters('app', [])
  },
  created() {},
  mounted() {
    this.init();
  },
  methods: {
    init() {
      this.visible = true;
      this.loading = false;
      this.$nextTick(() => {
        this.$refs.inputForm.resetFields();
        this.loading = true;
        MerchantInfoApi.merchantInfo().then((data) => {
          this.inputForm = this.$utils.recover(this.inputForm, data);
          this.loading = false;
        });
      });
    }
  }
};
</script>
<style lang="scss" scoped>
.info-value {
  font-size: 16px;
  font-weight: bold;
  color: #333;
}
.info-img {
  border-radius: 6pxs;
  border: 1px solid #ddd;
  width: 150px;
  height: 150px;
}
</style>