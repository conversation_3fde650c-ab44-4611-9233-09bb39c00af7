<template>
  <div>
    <el-dialog v-model="visible" :title="title" width="75%" style="margin-top: 20px;" :close-on-click-modal="false" draggable>
      <el-form ref="inputForm" :model="inputForm" v-loading="loading" label-width="120px">
        <el-tabs v-model="activeName" type="border-card">
          <el-tab-pane label="基本信息" name="info">
            <el-row :gutter="15">
              <el-col :span="24">
                <el-form-item label="商品名称" prop="name" :rules="[nonull]">
                  <el-input v-model="inputForm.name" placeholder="请输入类型名称"></el-input>
                </el-form-item>
              </el-col>

              <el-col :span="12">
                <el-form-item label="商品类型" prop="serviceGoods" :rules="[nonull]">
                  <el-radio-group v-model="inputForm.serviceGoods" @change="getGoodsTypeList(true)">
                    <el-radio :value="1">服务类</el-radio>
                    <el-radio :value="2">实物商品</el-radio>
                  </el-radio-group>
                </el-form-item>
              </el-col>

              <el-col :span="12">
                <el-form-item label="商品分类" prop="goodsTypeId" :rules="[nonull]">
                  <el-select v-model="inputForm.goodsTypeId" placeholder="请选择商品分类">
                    <el-option v-for="item in goodsTypeList" :key="item.id" :label="item.name" :value="item.id"></el-option>
                  </el-select>
                </el-form-item>
              </el-col>

              <el-col :span="24">
                <el-form-item label="商品描述" prop="goodsDesc">
                  <!-- <el-input v-model="inputForm.goodsDesc" :rows="2" type="textarea" show-word-limit maxlength="500" placeholder="请输入描述"></el-input> -->
                  <div style="height: 400px;width: 800px;">
                    <QuillEditor ref="quilleditor" v-model="inputForm.goodsDesc" style="height: 340px;width: 800px;"></QuillEditor>
                  </div>

                </el-form-item>
              </el-col>

              <el-col :span="24">
                <el-form-item label="图片" prop="pic" :rules="[nonull]">
                  <el-upload v-model:file-list="fileList" :headers="headers" :action="uploadUrl" list-type="picture-card" :on-success="handleSuccess"
                    :on-remove="handleRemove" :on-preview="handlePictureCardPreview" :limit="5" multiple>
                    <el-icon v-if="fileList.length < 5">
                      <Plus />
                    </el-icon>
                  </el-upload>
                </el-form-item>
              </el-col>

              <el-col :span="24">
                <el-form-item label="详情图片" prop="goodsDetailsPic" :rules="[nonull]">
                  <el-upload v-model:file-list="detailsFileList" :headers="headers" :action="uploadUrl" list-type="picture-card"
                    :on-success="handleDetailsSuccess" :on-remove="handleDetailsRemove" :on-preview="handleDetailsPictureCardPreview" :limit="8" multiple>
                    <el-icon v-if="detailsFileList.length < 8">
                      <Plus />
                    </el-icon>
                  </el-upload>
                </el-form-item>
              </el-col>

              <el-col :span="12">
                <el-form-item label="审核通过后是否立即上架" prop="display" :rules="[nonull]" label-width="190px">
                  <el-radio-group v-model="inputForm.display">
                    <el-radio :value="0">立即上架</el-radio>
                    <el-radio :value="1">手动上架</el-radio>
                  </el-radio-group>
                </el-form-item>
              </el-col>

            </el-row>
          </el-tab-pane>

          <el-tab-pane label="规格设置" name="spec">
            <el-row>
              <el-col :span="12">
                <el-form-item label="规格类型" prop="specType" :rules="[nonull]" @change="onSpecTypeChange">
                  <el-radio-group v-model="inputForm.specType">
                    <el-radio :value="1">无规格</el-radio>
                    <el-radio :value="2">多规格</el-radio>
                  </el-radio-group>
                </el-form-item>
              </el-col>
            </el-row>

            <el-row v-if="inputForm.specType === 1">
              <el-col :span="12">
                <el-form-item label="价格（元）" prop="price" :rules="[nonull]">
                  <el-input-number style="width: 200px;" v-model="inputForm.price" :min="0.01" placeholder="请输入价格" />
                </el-form-item>
              </el-col>
            </el-row>

            <div v-if="inputForm.specType === 2">
              <div v-for="(spec,index) in specAddData" :key="spec.id" style="margin-top: 10px;">
                <span>规格</span>
                <el-select style="width: 200px;margin-left: 16px;" v-model="spec.id" placeholder="请选择">
                  <el-option v-for="item in goodsSpecList" :disabled="hasDisabled(item.id)" :key="item.id" :label="item.name" :value="item.id"></el-option>
                </el-select>
                <span style="margin-left: 20px;">规格项</span>
                <el-select style="width: 300px;margin-left: 16px;" multiple v-model="spec.itemList" placeholder="请选择">
                  <el-option v-for="item in filterSpec(spec.id)" :key="item.id" :label="item.name" :value="item.id"></el-option>
                </el-select>

                <el-button style="margin-left: 20px;" type="danger" @click="delSpec(index)">删除</el-button>
              </div>

              <div style="display: block;margin-top: 20px;">
                <el-button type="primary" @click="addSpec">添加规格项</el-button>
                <el-button type="success" @click="composeSpec">生成规格</el-button>
              </div>

              <el-table v-if="specTableItemData.length>0" :data="specTableItemData" border style="margin-top: 20px;">
                <el-table-column type="index" label="#" width="55" align="center" />
                <el-table-column v-for="item in specTableData" :key="item.specId" :prop="item.specId" :label="item.specName"></el-table-column>

                <el-table-column label="价格（元）" width="200">
                  <template #default="scope">
                    <el-input-number style="width: 160px;" v-model="scope.row.price" :min="1" placeholder="请输入价格" />
                  </template>
                </el-table-column>

              </el-table>

            </div>

          </el-tab-pane>

        </el-tabs>

      </el-form>

      <template #footer>
        <span class="dialog-footer">
          <el-button size="large" @click="visible = false">关闭</el-button>
          <el-button size="large" type="success" v-if="method !== 'view'" :loading="loading" @click="doSubmit()">确定</el-button>
        </span>
      </template>

      <el-image-viewer v-if="imageViewerVisible" :url-list="inputForm.pic" show-progress @close="imageViewerVisible = false" />
      <el-image-viewer v-if="detailsImageViewerVisible" :url-list="inputForm.goodsDetailsPic" show-progress @close="detailsImageViewerVisible = false" />

    </el-dialog>
  </div>
</template>

<script>
import QuillEditor from '@/components/QuillEditor.vue';

import GoodsApi from '@/api/GoodsApi';
import GoodsTypeApi from '@/api/GoodsTypeApi';
import GoodsSpecApi from '@/api/GoodsSpecApi';
import AuditGoodsApi from '@/api/AuditGoodsApi';
import FileUploadApi from '@/api/FileUploadApi';
import { mapGetters } from 'vuex';

export default {
  data() {
    return {
      uploadUrl: FileUploadApi.merchantFileUpload(),
      title: '',
      activeName: 'info',
      nonull: { required: true, message: '必填' },
      method: '',
      visible: false,
      loading: false,
      inputForm: {
        id: '',
        name: '',
        goodsTypeId: null,
        goodsDesc: '',
        pic: [],
        serviceGoods: 0,
        display: 0,
        sellOut: 0,
        salesVolume: 0,
        sort: 0,
        specType: 1,
        specInfo: '',
        specData: [],
        goodsDetailsTxt: '',
        goodsDetailsPic: [],
        price: undefined,
        oldPrice: 0,
        updateGoodsId: ''
      },
      specIdNameMaps: {},
      specItemIdNameMaps: {},
      specAddData: [],
      specTableData: [],
      specTableItemData: [],
      goodsSpecList: [],
      goodsTypeList: [],
      headers: [],
      fileList: [],
      detailsFileList: [],
      imageViewerVisible: false,
      detailsImageViewerVisible: false
    };
  },
  components: { QuillEditor },
  computed: {
    ...mapGetters('app', ['authToken'])
  },
  created() {
    this.headers['Authorization'] = this.authToken;
  },
  mounted() {
    this.activeName = 'info';
  },
  methods: {
    filterSpec(specId) {
      if (specId) {
        return this.goodsSpecList.find((item) => item.id === specId).goodsSpecItems;
      } else {
        return [];
      }
    },
    addSpec() {
      this.specAddData.push({ id: '', itemList: [] });
    },
    delSpec(index) {
      this.specAddData.splice(index, 1);
    },
    getSpecName(id) {
      return this.goodsSpecList.find((item) => item.id == id).name;
    },
    composeSpec() {
      for (let i = 0; i < this.specAddData.length; i++) {
        if (!this.specAddData[i].id || this.specAddData[i].itemList.length === 0) {
          this.$message.error('规格信息不完整');
          return;
        }
      }

      const specItemList = [];

      const specInfoArr = [];

      this.specTableData = [];
      this.specTableItemData = [];
      this.specAddData.forEach((item) => {
        const temp = item.itemList.map((a) => {
          return { specItemId: a, specItemName: this.specItemIdNameMaps[a] };
        });
        specInfoArr.push({ specId: item.id, specName: this.specIdNameMaps[item.id], specItemList: temp });

        this.specTableData.push({ specId: item.id, specName: this.specIdNameMaps[item.id] });
        specItemList.push(item.itemList);
      });

      this.inputForm.specInfo = JSON.stringify(specInfoArr);

      const result = specItemList.reduce(
        (a, b) => {
          return a.flatMap((x) => {
            return b.map((y) => {
              return x.concat([y]);
            });
          });
        },
        [[]]
      );

      this.specTableItemData = result.map((item) => {
        let specItemResult = {};
        item.forEach((specItem, index) => {
          specItemResult[this.specTableData[index].specId] = this.specItemIdNameMaps[specItem];
          specItemResult.specIds = this.specTableData.map((a) => a.specId).join(',');
          specItemResult.specItemIds = item.join(',');
          specItemResult.price = undefined;
        });

        return specItemResult;
      });
    },
    hasDisabled(id) {
      return this.specAddData.some((item) => item.id == id);
    },
    getGoodsTypeList(flag) {
      if (flag) {
        this.inputForm.goodsTypeId = '';
      }
      GoodsTypeApi.goodsTypeList({ size: -1, current: 1 }, { serviceGoods: this.inputForm.serviceGoods }).then((data) => {
        this.goodsTypeList = data.records;
      });
    },
    getGoodsSpecApi() {
      GoodsSpecApi.goodsSpecList({ size: -1, current: 1 }).then((data) => {
        this.goodsSpecList = data.records;
        this.goodsSpecList.forEach((item) => {
          this.specIdNameMaps[item.id] = item.name;

          item.goodsSpecItems.forEach((specItem) => {
            this.specItemIdNameMaps[specItem.id] = specItem.name;
          });
        });
      });
    },
    onSpecTypeChange() {},
    handlePictureCardPreview() {
      this.imageViewerVisible = true;
    },
    handleDetailsPictureCardPreview() {
      this.detailsImageViewerVisible = true;
    },
    handleRemove(file) {
      this.inputForm.pic = this.inputForm.pic.filter((item) => {
        if (file.response && file.response.path) {
          return item != file.response.path;
        }
        return item != file.url;
      });
    },
    handleSuccess(response) {
      this.inputForm.pic.push(response.path);
    },
    handleDetailsRemove(file) {
      this.inputForm.goodsDetailsPic = this.inputForm.goodsDetailsPic.filter((item) => {
        if (file.response && file.response.path) {
          return item != file.response.path;
        }
        return item != file.url;
      });
    },
    handleDetailsSuccess(response) {
      this.inputForm.goodsDetailsPic.push(response.path);
    },
    async init(method, id) {
      this.method = method;
      this.inputForm.id = id;
      this.inputForm.updateGoodsId = '';
      if (method === 'add') {
        this.title = '新增商品';
      } else if (method === 'edit') {
        this.title = '修改';
      } else if (method === 'view') {
        this.title = '查看';
      }
      this.visible = true;
      this.loading = false;

      this.fileList = [];
      this.detailsFileList = [];
      this.specIdNameMaps = {};
      this.specItemIdNameMaps = {};
      this.specAddData = [];
      this.specTableData = [];
      this.specTableItemData = [];
      this.goodsSpecList = [];
      await this.getGoodsSpecApi();
      this.$nextTick(() => {
        this.$refs.inputForm.resetFields();

        if (method === 'edit' || method === 'view') {
          // 修改或者查看
          this.loading = true;
          AuditGoodsApi.auditGoodsInfo(this.inputForm.id).then((data) => {
            this.inputForm = this.$utils.recover(this.inputForm, data);
            if (this.inputForm.specType === 1) {
              const goodsItem = data.goodsItemList[0];
              this.inputForm.price = Number(goodsItem.price);
              this.inputForm.oldPrice = Number(goodsItem.oldPrice);
            }
            this.getGoodsTypeList(false);

            this.inputForm.pic = this.inputForm.pic.split(',');
            this.fileList = this.inputForm.pic.map((element) => {
              return { name: 'food.jpeg', url: element };
            });
            this.inputForm.goodsDetailsPic = this.inputForm.goodsDetailsPic.split(',');
            this.detailsFileList = this.inputForm.goodsDetailsPic.map((element) => {
              return { name: 'food.jpeg', url: element };
            });

            if (this.inputForm.specType === 2) {
              const specInfo = JSON.parse(this.inputForm.specInfo);
              specInfo.forEach((item) => {
                this.specAddData.push({
                  id: item.specId,
                  itemList: item.specItemList.map((a) => a.specItemId)
                });
              });
              this.composeSpec();

              this.specTableItemData.forEach((item, index) => {
                item.price = data.goodsItemList[index].price;
              });
            }

            this.loading = false;
          });
        } else if (method === 'goodsUpdate') {
          // 修改或者查看
          this.loading = true;
          GoodsApi.goodsInfo(id).then((data) => {
            this.inputForm = this.$utils.recover(this.inputForm, data);
            if (method === 'goodsUpdate') {
              this.inputForm.id = '';
              this.inputForm.updateGoodsId = id;
            }

            if (this.inputForm.specType === 1) {
              const goodsItem = data.goodsItemList[0];
              this.inputForm.price = Number(goodsItem.price);
              this.inputForm.oldPrice = Number(goodsItem.oldPrice);
            }
            this.getGoodsTypeList(false);

            this.inputForm.pic = this.inputForm.pic.split(',');
            this.fileList = this.inputForm.pic.map((element) => {
              return { name: 'food.jpeg', url: element };
            });
            this.inputForm.goodsDetailsPic = this.inputForm.goodsDetailsPic.split(',');
            this.detailsFileList = this.inputForm.goodsDetailsPic.map((element) => {
              return { name: 'food.jpeg', url: element };
            });

            if (this.inputForm.specType === 2) {
              const specInfo = JSON.parse(this.inputForm.specInfo);
              specInfo.forEach((item) => {
                this.specAddData.push({
                  id: item.specId,
                  itemList: item.specItemList.map((a) => a.specItemId)
                });
              });
              this.composeSpec();

              this.specTableItemData.forEach((item, index) => {
                item.price = data.goodsItemList[index].price;
              });
            }

            this.loading = false;
          });
        }
      });
    },
    // 表单提交
    doSubmit() {
      this.$refs['inputForm'].validate((valid) => {
        if (valid) {
          if (!this.inputForm.goodsDesc) {
            this.$message.warning('请填写商品描述');
            return;
          }

          this.loading = true;
          const params = Object.assign({}, this.inputForm);
          params.pic = params.pic.join(',');
          params.goodsDetailsPic = params.goodsDetailsPic.join(',');
          if (params.specType === 1) {
            params.specData = [{ price: params.price, oldPrice: 0 }];
          } else if (params.specType === 2) {
            if (this.specTableItemData.length == 0) {
              this.$message.error('请添加商品规格');
              return;
            }
            if (this.specTableItemData.some((item) => !item.price)) {
              this.$message.error('请补充价格');
              return;
            }
            params.specData = this.specTableItemData;
          }

          AuditGoodsApi.auditGoodsSave(params)
            .then(() => {
              this.visible = false;
              if (this.method === 'add' || this.method === 'goodsUpdate') {
                this.$message.success('商品已提交平台审核，请等待。查询审核状态请到商品审核记录中查看');
              } else {
                this.$message.success('操作成功');
              }
              this.$emit('refreshDataList');
              this.loading = false;
            })
            .catch(() => {
              this.loading = false;
            });
        }
      });
    }
  }
};
</script>