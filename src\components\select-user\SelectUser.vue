<template>
  <el-dialog v-model="value" title="选择用户" width="50%" :close-on-click-modal="false" draggable>
    <el-form ref="searchForm" :inline="true" :model="searchForm" label-width="80px">
      <el-form-item label="用户名" prop="name">
        <el-input v-model="searchForm.name" style="width:200px;" clearable />
      </el-form-item>
      <el-form-item>
        <el-button type="success" icon="search" @click="search">查询</el-button>
        <el-button type="warning" icon="refresh-left" @click="reset">重置</el-button>
      </el-form-item>
    </el-form>

    <el-table :data="tableData" border style="margin-top: 10px; " v-loading="loading" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column type="index" label="#" width="55" align="center" />
      <el-table-column prop="code" label="账号"></el-table-column>
      <el-table-column prop="name" label="用户名"></el-table-column>
      <el-table-column prop="mobile" label="手机号码"></el-table-column>
      <el-table-column prop="createTime" label="创建时间" width="200"></el-table-column>
    </el-table>
    <el-pagination style="margin-top: 10px;" ref="pagination" :total="pagination.total" v-model:current-page="pagination.current"
      v-model:page-size="pagination.size" :page-sizes="[10, 20, 50]" @size-change="handlePageSizeChange"
      @current-change="handlePageCurrentChange" layout="prev, pager, next, total, sizes">
    </el-pagination>

    <template #footer>
      <el-button size="large" @click="value = false">关闭</el-button>
      <el-button size="large" type="success" :loading="loading" @click="doSubmit()">确定</el-button>
    </template>

  </el-dialog>

</template>

<script>
import Config from '@/settings';
import SysUserApi from '@/api/SysUserApi';
import { nextTick } from 'vue';

export default {
  data() {
    return {
      tableData: [],
      searchForm: {
        name: '',
        status: ''
      },
      pagination: {
        total: 0,
        size: Config.pageSize,
        current: 1
      },
      loading: false,
      multipleSelection: []
    };
  },
  props: {
    modelValue: { type: Boolean }
  },
  watch: {
    modelValue(newVal) {
      this.$emit('update:modelValue', newVal); // 通知父组件更新 modelValue 的值
      if (newVal) {
        this.init();
      }
    }
  },
  computed: {
    value: {
      get() {
        return this.modelValue;
      },
      set(value) {
        this.$emit('update:modelValue', value);
      }
    }
  },
  components: {},
  created() {},
  methods: {
    handleSelectionChange(val) {
      this.multipleSelection = val;
    },
    init() {
      nextTick(() => {
        this.reset();
      });
    },
    search() {
      this.pagination.current = 1;
      this.refreshList();
    },
    reset() {
      this.$refs.searchForm.resetFields();
      this.pagination.current = 1;
      this.refreshList();
    },
    refreshList() {
      SysUserApi.sysUserList(this.pagination, this.searchForm).then((data) => {
        this.tableData = data.records;
        this.pagination.total = data.total;
        // 删除后如果当前页没数据,则返回前一页
        if (data.records.length === 0 && data.total > 0 && this.pagination.current > 1) {
          this.pagination.current = this.pagination.current - 1;
          this.refreshList();
        }
      });
    },
    handlePageSizeChange() {
      this.pagination.current = 1;
      this.refreshList();
    },
    handlePageCurrentChange() {
      this.refreshList();
    },
    doSubmit() {
      this.$emit('select', JSON.parse(JSON.stringify(this.multipleSelection)));
    }
  }
};
</script>