import { createStore } from 'vuex';
import createPersistedState from 'vuex-persistedstate';
import app from './modules/app.js';
import tags from './modules/tags.js';
import router from './modules/router.js';

// export default createStore({
// 	// state '全局状态'
// 	state: {},
// 	// 集中式修改状态,这里修改会被`监听`
// 	mutations: {},
// 	// 异步
// 	actions: {},
// 	// 模块化
// 	modules: {},
// 	plugins: [createPersistedState({
// 		storage: window.sessionStorage
// 	})]
// })
const modules = { app, tags, router };
export default createStore({
  modules,
  plugins: [
    createPersistedState({
      storage: window.sessionStorage
    })
  ],
  strict: import.meta.env.DEV
});
