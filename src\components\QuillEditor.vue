<template>
  <div class="editor-box">
    <QuillEditor content-type="html" v-model:content="value" :options="editorOption" ref="quillEditor" class="quill-editor" />
  </div>
</template>
<script>
export default {
  data() {
    return {
      editorOption: {
        modules: {
          toolbar: [
            ['bold', 'italic', 'underline', 'strike'], // toggled buttons
            ['blockquote', 'code-block'],
            [{ align: [] }], // 对齐方式
            [{ color: [] }, { background: [] }], // 字体颜色和背景色
            // ['link', 'image', 'video', 'formula'],

            // 工具栏配置
            [{ list: 'ordered' }, { list: 'bullet' }], // 有序列表和无序列表
            [{ indent: '-1' }, { indent: '+1' }], // 缩进
            [{ header: [1, 2, 3, 4, 5, 6, false] }], // 标题等级
            // [{ font: [] }], // 字体
            ['clean'] // 清除格式
          ]
        },
        placeholder: '请输入内容...',
        theme: 'snow'
      }
    };
  },
  props: { modelValue: { type: String, default: '' } },
  watch: {
    modelValue(newVal) {
      this.$emit('update:modelValue', newVal); // 通知父组件更新 modelValue 的值
    }
  },
  computed: {
    value: {
      get() {
        return this.modelValue;
      },
      set(value) {
        this.$emit('update:modelValue', value);
      }
    }
  },
  components: {},
  created() {},
  methods: {
    setContent(data) {
      // 获取 Quill 实例
      const quillInstance = this.$refs.quillEditor.getQuill();
      // 使用 Quill 的 updateContents 或 setText 方法来设置内容
      quillInstance.clipboard.dangerouslyPasteHTML(data);
      // 更新 Vue 状态
      // content.value = data;
      this.emit('update:modelValue', data);
    },
    clearContent() {
      // 获取 Quill 实例
      const quillInstance = this.$refs.quillEditor.getQuill();
      // 清除内容
      quillInstance.setContents([]);
      // 可选：同步 v-model 的值
      this.emit('update:modelValue', '');
    }
  }
};
</script>