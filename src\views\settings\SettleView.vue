<template>
  <div style="padding: 20px;">
    <el-form ref="inputForm" :model="inputForm" :disabled="disabled" v-loading="loading" label-width="120px" label-position="top">
      <el-row :gutter="15">

        <el-col :span="24">
          <el-form-item label="开户银行" prop="bankName" :rules="[notNull]">
            <el-input v-model="inputForm.bankName" placeholder="请输入开户银行" style="width: 300px;"></el-input>
          </el-form-item>
        </el-col>

        <el-col :span="24">
          <el-form-item label="银行账号" prop="bankAccount" :rules="[notNull]">
            <el-input v-model="inputForm.bankAccount" placeholder="请输入银行账号" style="width: 300px;"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="开户人姓名" prop="bankAccountHolder" :rules="[notNull]">
            <el-input v-model="inputForm.bankAccountHolder" placeholder="请输入开户人姓名" style="width: 300px;"></el-input>
          </el-form-item>
        </el-col>

      </el-row>

    </el-form>
    <div style="display: flex;flex-direction: row;justify-content: space-around;margin-top: 20px;width: 300px;">
      <el-button v-if="disabled" type="primary" style="" @click.prevent="disabled=false">修 改</el-button>
      <el-button v-if="!disabled" :loading="loading" type="primary" style="" @click.prevent="doSubmit">提 交</el-button>
      <el-button v-if="!disabled" type="info" style="" @click.prevent="cancel">取 消</el-button>
    </div>

  </div>
</template>

<script>
import MerchantInfoApi from '@/api/MerchantInfoApi';
import { mapGetters } from 'vuex';

export default {
  name: 'settleAccounts',
  data() {
    return {
      notNull: { required: true, message: '必填' },
      disabled: true,
      loading: false,
      inputForm: {
        id: '',
        bankName: '',
        bankAccount: '',
        bankAccountHolder: ''
      }
    };
  },
  components: {},
  computed: {
    ...mapGetters('app', [])
  },
  created() {},
  mounted() {
    this.init();
  },
  methods: {
    cancel() {
      this.init();
      this.disabled = true;
    },
    doSubmit() {
      this.$refs['inputForm'].validate((valid) => {
        if (valid) {
          this.loading = true;
          MerchantInfoApi.merchantInfoUpdateSettle(this.inputForm)
            .then((data) => {
              this.$message.success('操作成功');
              this.loading = false;
              this.inputForm = this.$utils.recover(this.inputForm, data);
              this.disabled = true;
            })
            .catch(() => {
              this.loading = false;
            });
        }
      });
    },
    init() {
      this.disabled = true;
      this.loading = false;
      this.$nextTick(() => {
        this.$refs.inputForm.resetFields();
        this.loading = true;
        MerchantInfoApi.merchantInfo().then((data) => {
          this.inputForm = this.$utils.recover(this.inputForm, data);
          this.loading = false;
        });
      });
    }
  }
};
</script>
<style lang="scss" scoped>
.info-value {
  font-size: 16px;
  font-weight: bold;
  color: #333;
}
.info-img {
  border-radius: 6pxs;
  border: 1px solid #ddd;
  width: 150px;
  height: 150px;
}
</style>