<template>
  <template v-if="!item.meta.hidden">
    <el-menu-item v-if="!item.children || !item.children.length" :index="item.path">
      <!-- <icon-svg v-if="item.meta.icon" :name="item.meta.icon" class="icon-svg-item" /> -->
      <!-- <i class="icon" :class="item.meta.icon" /> -->
      <!-- <font-awesome-icon :icon="item.meta.icon" /> -->
      <component v-if="item.meta.icon && item.meta.icon.split(':')[0] == 'el'" :is="item.meta.icon.split(':')[1]" class="icon-img">
      </component>
      <font-awesome-icon v-else-if="item.meta.icon && item.meta.icon.split(':').length>0"
        :icon="[item.meta.icon.split(':')[0], item.meta.icon.split(':')[1]]" class="icon-img" />

      <span class="title">{{ item.meta.title }}</span>
    </el-menu-item>

    <el-sub-menu v-else :index="item.path">
      <template #title>
        <!-- <icon-svg v-if="item.meta.icon" :name="item.meta.icon" class="icon-svg-item" /> -->
        <!-- <i class="icon" :class="item.meta.icon" /> -->
        <!-- <font-awesome-icon :icon="item.meta.icon" /> -->
        <component v-if="item.meta.icon && item.meta.icon.split(':')[0] == 'el'" :is="item.meta.icon.split(':')[1]" class="icon-img">
        </component>
        <font-awesome-icon v-else-if="item.meta.icon && item.meta.icon.split(':').length>0"
          :icon="[item.meta.icon.split(':')[0], item.meta.icon.split(':')[1]]" class="icon-img" />
        <span class="title">{{ item.meta.title }}</span>
      </template>
      <side-menu-item v-for="child in item.children" :key="child.path" :item="child" />
    </el-sub-menu>
  </template>
</template>

<script>
export default {
  name: 'side-menu-item',
  data() {
    return {};
  },
  props: {
    item: Object
  }
};
</script>

<style lang="scss" scoped>
.icon-img {
  // margin: 0 15px 0 5px;
  width: 14px;
  font-size: 14px;
}

.title {
  margin-left: 10px;
}
</style>