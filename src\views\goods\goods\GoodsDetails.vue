<template>
  <div>
    <el-dialog v-model="visible" title="商品详情" width="1200px" style="margin-top: 50px;" :close-on-click-modal="false" draggable>
      <el-form ref="inputForm" :model="inputForm" v-loading="loading" label-width="80px" label-position="left">
        <el-tabs v-model="activeName">
          <el-tab-pane label="基本信息" :name="0">

            <el-row :gutter="15">
              <el-col :span="12">
                <el-form-item label="商品名" prop="name">
                  <div class="info-value">{{ inputForm.name }}</div>
                </el-form-item>
              </el-col>
              <el-col :span="6">
                <el-form-item label="所属分类" prop="goodsTypeName">
                  <div class="info-value">{{ inputForm.goodsTypeName }}</div>
                </el-form-item>
              </el-col>
              <el-col :span="6">
                <el-form-item label="商品类型" prop="serviceGoods">
                  <div class="info-value">{{ inputForm.serviceGoods==1?"虚拟":"实物" }}</div>
                </el-form-item>
              </el-col>

              <el-col :span="24">
                <el-form-item label="商品描述" prop="goodsDesc">
                  <div class="info-value ql-editor" style="border: 1px solid #eee;border-radius: 3px;padding: 5px;width: 800px;" v-html="inputForm.goodsDesc">
                  </div>
                </el-form-item>
              </el-col>

              <el-col :span="24">
                <el-form-item label="商品图片" prop="pic">
                  <template v-for="(item,index) in inputForm.pic.split(',')" :key="index">
                    <el-image class="info-img" :z-index="9999" preview-teleported :src="item" :preview-src-list="inputForm.pic.split(',')"
                      :initial-index="index" :teleported="true" fit="cover" />
                  </template>
                </el-form-item>
              </el-col>

              <el-col :span="24">
                <el-form-item label="详情图片" prop="goodsDetailsPic">
                  <template v-if="inputForm.goodsDetailsPic">
                    <template v-for="(item,index) in inputForm.goodsDetailsPic.split(',')" :key="index">
                      <el-image class="info-img" :z-index="9999" preview-teleported :src="item" :preview-src-list="inputForm.goodsDetailsPic.split(',')"
                        :initial-index="index" :teleported="true" fit="cover" />
                    </template>
                  </template>
                  <div v-if="!inputForm.goodsDetailsPic" class="info-value">无</div>
                </el-form-item>
              </el-col>

              <el-col :span="6">
                <el-form-item label="是否上架" prop="display">
                  <div class="info-value">
                    <el-tag v-if="inputForm.display === 0" type="success">上架中</el-tag>
                    <el-tag v-else-if="inputForm.display === 1" type="info">已下架</el-tag>
                  </div>
                </el-form-item>
              </el-col>

              <!-- <el-col :span="6">
                <el-form-item label="售卖状态" prop="sellOut">
                  <div class="info-value">
                    <el-tag v-if="inputForm.sellOut === 0" type="success">正常</el-tag>
                    <el-tag v-else-if="inputForm.sellOut === 1" type="info">售罄</el-tag>
                  </div>
                </el-form-item>
              </el-col> -->

            </el-row>
          </el-tab-pane>
        </el-tabs>

        <el-tabs v-model="activeName">
          <el-tab-pane label="商品规格" :name="0">

            <el-table v-if="specTableItemData.length>0" :data="specTableItemData" border style="margin-top: 20px;">
              <el-table-column type="index" label="#" width="55" align="center" />
              <el-table-column v-for="item in specTableData" :key="item.specId" :prop="item.specId" :label="item.specName"></el-table-column>
              <el-table-column label="价格（元）" prop="price" width="200"></el-table-column>
            </el-table>

          </el-tab-pane>

        </el-tabs>

      </el-form>

      <template #footer>
        <span class="dialog-footer">
          <el-button size="large" @click="visible = false">关闭</el-button>
        </span>
      </template>

    </el-dialog>
  </div>
</template>

<script>
import GoodsSpecApi from '@/api/GoodsSpecApi';
import GoodsApi from '@/api/GoodsApi';
import { mapGetters } from 'vuex';

export default {
  props: {},
  data() {
    return {
      activeName: 0,
      title: '',
      nonull: { required: true, message: '必填' },
      visible: false,
      loading: false,
      inputForm: {
        id: '',
        name: '',
        pic: '',
        goodsTypeName: '',
        serviceGoods: '',
        display: '',
        sellOut: '',
        goodsDesc: '',
        specType: '',
        specInfo: '',
        goodsDetailsPic: '',
        remark: '',
        createTime: '',
        modifiedTime: '',
        goodsItemList: []
      },
      specIdNameMaps: {},
      specItemIdNameMaps: {},
      specAddData: [],
      specTableData: [],
      specTableItemData: [],
      goodsSpecList: []
    };
  },
  components: {},
  computed: {
    ...mapGetters('app', [])
  },
  created() {},
  mounted() {},
  methods: {
    async getGoodsSpecApi(merchantId) {
      const data = await GoodsSpecApi.goodsSpecList({ size: -1, current: 1 }, { merchantId: merchantId });
      this.goodsSpecList = data.records;
      this.goodsSpecList.forEach((item) => {
        this.specIdNameMaps[item.id] = item.name;

        item.goodsSpecItems.forEach((specItem) => {
          this.specItemIdNameMaps[specItem.id] = specItem.name;
        });
      });
    },
    composeSpec() {
      const specItemList = [];
      const specInfoArr = [];

      this.specTableData = [];
      this.specTableItemData = [];
      this.specAddData.forEach((item) => {
        const temp = item.itemList.map((a) => {
          return { specItemId: a, specItemName: this.specItemIdNameMaps[a] };
        });
        specInfoArr.push({ specId: item.id, specName: this.specIdNameMaps[item.id], specItemList: temp });

        this.specTableData.push({ specId: item.id, specName: this.specIdNameMaps[item.id] });
        specItemList.push(item.itemList);
      });

      this.inputForm.specInfo = JSON.stringify(specInfoArr);

      const result = specItemList.reduce(
        (a, b) => {
          return a.flatMap((x) => {
            return b.map((y) => {
              return x.concat([y]);
            });
          });
        },
        [[]]
      );

      this.specTableItemData = result.map((item) => {
        let specItemResult = {};
        item.forEach((specItem, index) => {
          specItemResult[this.specTableData[index].specId] = this.specItemIdNameMaps[specItem];
          specItemResult.specIds = this.specTableData.map((a) => a.specId).join(',');
          specItemResult.specItemIds = item.join(',');
          specItemResult.price = undefined;
        });

        return specItemResult;
      });
    },
    init(id) {
      this.inputForm.id = id;
      this.visible = true;
      this.loading = false;
      this.specIdNameMaps = {};
      this.specItemIdNameMaps = {};
      this.specAddData = [];
      this.specTableData = [];
      this.specTableItemData = [];
      this.goodsSpecList = [];

      this.$nextTick(() => {
        this.$refs.inputForm.resetFields();
        this.loading = true;
        GoodsApi.goodsInfo(this.inputForm.id).then((data) => {
          this.inputForm = this.$utils.recover(this.inputForm, data);
          if (this.inputForm.specType === 1) {
            const goodsItem = data.goodsItemList[0];
            this.specTableData = [{ specId: '规格', specName: '规格' }];
            this.specTableItemData = [{ 规格: '默认', price: goodsItem.price }];
          } else if (this.inputForm.specType === 2) {
            const specInfo = JSON.parse(this.inputForm.specInfo);
            specInfo.forEach((item) => {
              this.specAddData.push({
                id: item.specId,
                itemList: item.specItemList.map((a) => a.specItemId)
              });
            });

            this.getGoodsSpecApi(data.merchantId).then(() => {
              this.composeSpec();
              this.specTableItemData.forEach((item, index) => {
                item.price = data.goodsItemList[index].price;
              });
            });
          }

          this.loading = false;
        });
      });
    }
  }
};
</script>

<style lang="scss" scoped>
.info-value {
  font-size: 14px;
  font-weight: bold;
  color: #333;
}

.info-img {
  padding: 5px;
  border-radius: 6pxs;
  border: 1px solid #f2f2f2;
  width: 60px;
  height: 60px;
  margin-right: 10px;
}
</style>