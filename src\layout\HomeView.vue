<template>
  <div class="home-wrapper">
    <el-container>
      <el-aside class="left-container">
        <logo />
        <el-scrollbar class="scrollbar-wrapper">
          <side-menu />
        </el-scrollbar>
      </el-aside>
      <el-container>
        <el-header class="header-container">
          <nav-bar />
          <tags-view />
        </el-header>

        <el-main>
          <app-main />
        </el-main>
      </el-container>
    </el-container>
  </div>
</template>

<script>
import Logo from './Logo.vue';
import NavBar from './NavBar.vue';
import TagsView from './TagsView.vue';
import AppMain from './AppMain.vue';
import SideMenu from './Menu/SideMenu.vue';
import variables from '@/assets/styles/variables.module.scss';
// import LoginApi from '@/api/LoginApi';

export default {
  name: 'Home',
  data() {
    return {};
  },
  components: { Logo, NavBar, TagsView, AppMain, SideMenu },
  computed: {
    variables() {
      return variables;
    }
  },
  mounted() {
    // LoginApi.getInfo()
    //   .then((data) => {
    //     this.$store.commit('app/setUser', data);
    //   })
    //   .catch(() => {
    //     this.$router.push('/Login');
    //   });
  },
  methods: {}
};
</script>

<style lang="scss" scoped>
@use '@assets/styles/variables.module.scss' as *;

.left-container {
  width: $sideBarWidth !important;
  background-color: $menuBg;

  .scrollbar-wrapper {
    height: calc(100vh - 51px);
    overflow-x: hidden !important;
  }
  .el-menu {
    border: none;
    height: 100%;
    width: 100% !important;
  }
}
.header-container {
  height: 100px;
  padding: 0;
}
.el-main {
  height: calc(100vh - 100px);
  padding: 0;
  overflow-x: hidden;
}
</style>
