import router from './router';
import store from './store';
// cdn引入
import ElementPlus from 'element-plus';
// cdn引入
import * as ElementPlusIconsVue from '@element-plus/icons-vue';
import zhCn from 'element-plus/dist/locale/zh-cn.mjs';

import utils from './utils';

import 'element-plus/dist/index.css';

import { library } from '@fortawesome/fontawesome-svg-core';
import { fas } from '@fortawesome/free-solid-svg-icons';
import { fab } from '@fortawesome/free-brands-svg-icons';
import { far } from '@fortawesome/free-regular-svg-icons';
import { FontAwesomeIcon } from '@fortawesome/vue-fontawesome';

import { directive } from './utils/directive';
import { QuillEditor, Quill } from '@vueup/vue-quill';
import '@vueup/vue-quill/dist/vue-quill.snow.css';
export async function bootstrap(options) {
  const { app } = options;

  app.use(ElementPlus, { size: 'default', locale: zhCn, zIndex: 3000 });
  for (const [key, component] of Object.entries(ElementPlusIconsVue)) {
    app.component(key, component);
  }

  app.config.globalProperties.$utils = utils;
  app.use(store).use(router);

  store.commit('router/setIsFinish', false);
  store.commit('tags/INIT');

  library.add(fas);
  library.add(fab);
  library.add(far);
  app.component('font-awesome-icon', FontAwesomeIcon);

  // 注册 自定义指令
  directive(app);

  // 设置居中样式
  const Align = Quill.import('attributors/style/align'); // 引入这个后会把对齐类型样式写在style上
  Align.whitelist = ['right', 'center', 'justify'];
  Quill.register(Align, true);
  app.component('QuillEditor', QuillEditor);

  return { router };
}
