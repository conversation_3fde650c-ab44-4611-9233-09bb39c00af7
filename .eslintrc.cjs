module.exports = {
  root: true,
  env: {
    node: true,
    'vue/setup-compiler-macros': true
  },
  extends: ['plugin:vue/vue3-essential', 'eslint:recommended'],
  plugins: ['vue'],
  rules: {
    'vue/multi-word-component-names': 'off',
    quotes: [0, 'double'],
    indent: ['off', 2],
    'func-call-spacing': 'off',
    'space-before-function-paren': 0,
    'generator-star-spacing': 'off',
    curly: 'error'
  }
};
