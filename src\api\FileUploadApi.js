export default {
  merchantFileUpload() {
    if (import.meta.env.PROD) {
      return `/${import.meta.env.VITE_API_CONTENT}/merchant/file/upload`;
    } else {
      return '/merchant/file/upload';
    }
  },
  merchantRegFileUpload() {
    if (import.meta.env.PROD) {
      return `/${import.meta.env.VITE_API_CONTENT}/merchant/merchantInfo/uploadMerchantInfo`;
    } else {
      return '/merchant/merchantInfo/uploadMerchantInfo';
    }
  }
};
