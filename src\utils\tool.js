import constant from '@/utils/constant.js';

// 把路径转换成驼峰命名
export const pathToCamel = (path) => {
  return path.replace(/\/(\w)/g, (all, letter) => letter.toUpperCase());
};

// 是否外链
export const isExternalLink = (url) => {
  return /^(https?:|\/\/|http?:|\/\/|^{{\s?apiUrl\s?}})/.test(url);
};

// 替换外链参数
export const replaceLinkParam = (url) => {
  return url.replace('{{apiUrl}}', constant.apiUrl);
};

// 转换文件大小格式
export const convertSizeFormat = (size) => {
  const unit = ['Bytes', 'KB', 'MB', 'GB', 'TB', 'PB'];
  let index = Math.floor(Math.log(size) / Math.log(1024));
  let newSize = size / Math.pow(1024, index);

  // 保留的小数位数
  return newSize.toFixed(2) + ' ' + unit[index];
};

// 生成uuid
export const generateUUID = () => {
  let uuid = '';
  for (let i = 0; i < 32; i++) {
    let random = (Math.random() * 16) | 0;
    if (i === 8 || i === 12 || i === 16 || i === 20) {
      uuid += '-';
    }
    uuid += (i === 12 ? 4 : i === 16 ? (random & 3) | 8 : random).toString(16);
  }
  return uuid;
};

// 获取svg图标(id)列表
export const getIconList = () => {
  const rs = [];
  const list = document.querySelectorAll('svg symbol[id^="icon-"]');
  for (let i = 0; i < list.length; i++) {
    rs.push(list[i].id);
  }
  return rs;
};

// 获取字典Label
export const getDictLabel = (dictList, dictType, dictValue) => {
  const type = dictList.find((element) => element.dictType === dictType);
  if (type) {
    const val = type.dataList.find((element) => element.dictValue === dictValue + '');
    if (val) {
      return val.dictLabel;
    } else {
      return dictValue;
    }
  } else {
    return dictValue;
  }
};

// 获取字典Label样式
export const getDictLabelClass = (dictList, dictType, dictValue) => {
  const type = dictList.find((element) => element.dictType === dictType);
  if (type) {
    const val = type.dataList.find((element) => element.dictValue === dictValue + '');
    if (val) {
      return val.labelClass;
    } else {
      return '';
    }
  } else {
    return '';
  }
};

// 获取字典数据列表
export const getDictDataList = (dictList, dictType) => {
  const type = dictList.find((element) => element.dictType === dictType);
  if (type) {
    return type.dataList;
  } else {
    return [];
  }
};

// 树形数据转换
export const treeDataTranslate = (data, id, pid) => {
  const res = [];
  const temp = {};
  id = id || 'id';
  pid = pid || 'pid';
  for (let i = 0; i < data.length; i++) {
    temp[data[i][id]] = data[i];
  }
  for (let k = 0; k < data.length; k++) {
    if (!temp[data[k][pid]] || data[k][id] === data[k][pid]) {
      res.push(data[k]);
      continue;
    }
    if (!temp[data[k][pid]]['children']) {
      temp[data[k][pid]]['children'] = [];
    }
    temp[data[k][pid]]['children'].push(data[k]);
  }
  return res;
};

// 生成数字字母混合字符串
export const getRandom = (num) => {
  const chars = [
    '0',
    '1',
    '2',
    '3',
    '4',
    '5',
    '6',
    '7',
    '8',
    '9',
    'a',
    'b',
    'c',
    'd',
    'e',
    'f',
    'g',
    'h',
    'i',
    'j',
    'k',
    'l',
    'm',
    'n',
    'o',
    'p',
    'q',
    'r',
    's',
    't',
    'u',
    'v',
    'w',
    'x',
    'y',
    'z'
  ];
  let res = '';
  for (let i = 0; i < num; i++) {
    const id = Math.floor(Math.random() * 36);
    res += chars[id];
  }
  return res;
};
