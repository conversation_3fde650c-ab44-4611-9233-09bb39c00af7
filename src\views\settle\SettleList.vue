<template>
  <div class="app-container">
    <el-form ref="searchForm" :model="searchForm" label-width="80px">
      <el-row>
        <el-col :span="6">
          <el-form-item label="结算单号" prop="settleId">
            <el-input v-model="searchForm.settleId" style="width:200px;" clearable />
          </el-form-item>
        </el-col>

        <el-col :span="6">
          <el-form-item label="商家" prop="merchantName">
            <el-input v-model="searchForm.merchantName" style="width:200px;" clearable />
          </el-form-item>
        </el-col>

        <el-col :span="6">
          <el-form-item label="结算日期" prop="bcName">
            <el-date-picker v-model="searchForm.searchDate" type="daterange" value-format="YYYY-MM-DD 00:00:00" range-separator="至" start-placeholder="开始日期"
              end-placeholder="截止日期" />
          </el-form-item>
        </el-col>

        <!-- <el-col :span="6">
          <el-form-item label="结算状态" prop="settleStatus">
            <el-select v-model="searchForm.settleStatus" placeholder="请选择结算状态">
              <el-option label="待结算" :value="0"></el-option>
              <el-option label="商户已确认" :value="1"></el-option>
              <el-option label="平台已确认" :value="2"></el-option>
              <el-option label="已完成" :value="10"></el-option>
            </el-select>
          </el-form-item>
        </el-col> -->

        <el-col :span="6">
          <el-form-item>
            <el-button type="success" icon="search" @click="search">查询</el-button>
            <el-button type="warning" icon="refresh-left" @click="reset">重置</el-button>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>

    <el-table :data="tableData" border style="margin-top: 10px; " v-loading="loading">
      <el-table-column type="index" label="#" width="55" align="center" />
      <el-table-column prop="settleId" label="结算单号" width="230">
        <template #default="scope">
          <el-link type="primary" underline="never" @click="toDetail(scope.row)">{{ scope.row.settleId }}</el-link>
        </template>
      </el-table-column>
      <el-table-column prop="merchantName" label="商户"></el-table-column>
      <el-table-column label="结算周期" width="220">
        <template #default="scope">
          {{ scope.row.startTime.substring(0, 10) }} ~ {{ scope.row.endTime.substring(0, 10) }}
        </template>
      </el-table-column>
      <el-table-column prop="total" label="结算金额" width="120">
        <template #default="scope">
          ￥ {{ scope.row.total }}
        </template>
      </el-table-column>
      <el-table-column prop="settleCommission" label="平台佣金" width="120">
        <template #default="scope">
          ￥ {{ scope.row.settleCommission }}
        </template>
      </el-table-column>

      <!-- <el-table-column prop="settleStatus" label="结算状态" width="130">
        <template #default="scope">
          <el-tag v-if="scope.row.settleStatus === 0" type="warning">待结算</el-tag>
          <el-tag v-else-if="scope.row.settleStatus === 1" type="primary">商户已确认</el-tag>
          <el-tag v-else-if="scope.row.settleStatus === 2" type="primary">平台已确认</el-tag>
          <el-tag v-else-if="scope.row.settleStatus === 10" type="success">已完成</el-tag>
        </template>
      </el-table-column> -->
      <!-- <el-table-column label="操作">
        <template #default="scope">
          <el-button v-if="scope.row.auditStatus === 0" type="primary" icon="edit" @click="toEdit(scope.row)" />
          <el-button v-if="scope.row.auditStatus === 0" type="danger" icon="delete" @click="toDel(scope.row)" />
        </template>
      </el-table-column> -->

    </el-table>
    <el-pagination style="margin-top: 10px;" ref="pagination" :total="pagination.total" v-model:current-page="pagination.current"
      v-model:page-size="pagination.size" :page-sizes="[10, 20, 50]" @size-change="handlePageSizeChange" @current-change="handlePageCurrentChange"
      layout="prev, pager, next, total, sizes">
    </el-pagination>

    <SettleDetails ref="settleDetails" @refreshDataList="refreshList"></SettleDetails>
  </div>
</template>

<script>
import SettleDetails from './SettleDetails.vue';
import Config from '@/settings';
import SettleApi from '@/api/SettleApi';
// import { ElMessageBox } from 'element-plus';
// import { h } from 'vue';

export default {
  name: 'SettleList',
  data() {
    return {
      tableData: [],
      goodsTypeList: [],
      searchForm: {
        settleId: '',
        merchantName: '',
        settleStatus: null,
        searchDate: []
      },
      pagination: {
        total: 0,
        size: Config.pageSize,
        current: 1
      },
      loading: false
    };
  },
  components: { SettleDetails },
  activated() {
    this.refreshList();
  },
  methods: {
    toDetail(row) {
      this.$refs.settleDetails.init(row.settleId);
    },
    search() {
      this.pagination.current = 1;
      this.refreshList();
    },
    reset() {
      this.$refs.searchForm.resetFields();
      this.pagination.current = 1;
      this.refreshList();
    },
    refreshList() {
      this.loading = true;
      const params = Object.assign({}, this.searchForm);
      if (params.searchDate.length > 0) {
        params.startTime = params.searchDate[0];
        params.endTime = params.searchDate[1];
      }
      SettleApi.settleList(this.pagination, params).then((data) => {
        this.tableData = data.records;
        this.pagination.total = data.total;
        // 删除后如果当前页没数据,则返回前一页
        if (data.records.length === 0 && data.total > 0 && this.pagination.current > 1) {
          this.pagination.current = this.pagination.current - 1;
          this.refreshList();
        }
        this.loading = false;
      });
    },
    handlePageSizeChange() {
      this.pagination.current = 1;
      this.refreshList();
    },
    handlePageCurrentChange() {
      this.refreshList();
    }
  }
};
</script>

<style lang="scss" scoped>
</style>