<template>
  <div>
    <el-dialog v-model="visible" title="客户详情" width="1200px" style="margin-top: 50px;" :close-on-click-modal="false" draggable>
      <el-form ref="inputForm" :model="inputForm" v-loading="loading" label-width="120px" label-position="top">
        <el-row :gutter="15">
          <el-col :span="6">
            <el-form-item label="用户昵称" prop="nickName">
              <div class="info-value">{{ inputForm.nickName }}</div>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="手机号码" prop="mobile">
              <div class="info-value">{{ inputForm.mobile }}</div>
            </el-form-item>
          </el-col>

        </el-row>

        <el-tabs v-model="activeName">
          <el-tab-pane label="历史订单" :name="0">
            <CustomerOrderList ref="customerOrderList" :userId="inputForm.id"></CustomerOrderList>
          </el-tab-pane>

          <el-tab-pane label="潜在订单" :name="1">
            <CustomerPotentialOrderList ref="customerPotentialOrderList" :userId="inputForm.id"></CustomerPotentialOrderList>
          </el-tab-pane>
        </el-tabs>

      </el-form>

      <template #footer>
        <span class="dialog-footer">
          <el-button size="large" @click="visible = false">关闭</el-button>
        </span>
      </template>

    </el-dialog>
  </div>
</template>

<script>
import CustomerOrderList from './CustomerOrderList.vue';
import CustomerPotentialOrderList from './CustomerPotentialOrderList.vue';
import MerchantUserApi from '@/api/MerchantUserApi';
import { mapGetters } from 'vuex';

export default {
  props: {},
  data() {
    return {
      activeName: 0,
      title: '',
      nonull: { required: true, message: '必填' },
      visible: false,
      loading: false,
      inputForm: {
        id: '',
        nickName: '',
        mobile: ''
      }
    };
  },
  components: { CustomerOrderList, CustomerPotentialOrderList },
  computed: {
    ...mapGetters('app', [])
  },
  created() {},
  mounted() {},
  methods: {
    init(id) {
      this.inputForm.id = id;
      this.visible = true;
      this.loading = false;
      this.$nextTick(() => {
        this.$refs.inputForm.resetFields();
        this.loading = true;
        MerchantUserApi.merchantCustomerInfo(this.inputForm.id).then((data) => {
          this.inputForm = this.$utils.recover(this.inputForm, data);
          this.loading = false;
        });
      });
    }
  }
};
</script>

<style lang="scss" scoped>
.info-value {
  font-size: 16px;
  font-weight: bold;
  color: #333;
}

.info-img {
  border-radius: 6pxs;
  border: 1px solid #ddd;
  width: 150px;
  height: 150px;
  margin-right: 20px;
}
</style>