<template>
  <div>
    <el-form ref="searchForm" :model="searchForm" label-width="80px">
      <el-row>
        <!-- <el-col :span="6">
          <el-form-item label="订单号" prop="orderCode">
            <el-input v-model="searchForm.orderCode" style="width:200px;" clearable />
          </el-form-item>
        </el-col> -->
        <!-- <el-col :span="6">
          <el-form-item label="商品类型" prop="serviceGoods">
            <el-select v-model="searchForm.serviceGoods" placeholder="请选择商品类型">
              <el-option label="服务类" value="1"></el-option>
              <el-option label="实物商品" value="2"></el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="商品分类" prop="goodsTypeId">
            <el-select v-model="searchForm.goodsTypeId" placeholder="请选择商品分类">
              <el-option v-for="item in goodsTypeList" :key="item.id" :label="item.name" :value="item.id"></el-option>
            </el-select>
          </el-form-item>
        </el-col> -->
        <el-col :span="6">
          <el-form-item>
            <el-button type="success" icon="search" @click="search">查询</el-button>
            <el-button type="warning" icon="refresh-left" @click="reset">重置</el-button>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>

    <el-table :data="tableData" border style="margin-top: 10px; " v-loading="loading">
      <el-table-column type="index" label="#" width="55" align="center" />
      <el-table-column prop="goods.name" label="商品名称"></el-table-column>
      <el-table-column prop="goodsNum" label="数量"></el-table-column>
      <el-table-column prop="createTime" label="添加时间" width="140">
        <template #default="scope">
          {{ scope.row.createTime.substring(0, 16) }}
        </template>
      </el-table-column>

    </el-table>
    <el-pagination style="margin-top: 10px;" ref="pagination" :total="pagination.total" v-model:current-page="pagination.current"
      v-model:page-size="pagination.size" :page-sizes="[10, 20, 50]" @size-change="handlePageSizeChange"
      @current-change="handlePageCurrentChange" layout="prev, pager, next, total, sizes">
    </el-pagination>

    <OrderDetails ref="orderDetails" @refreshDataList="refreshList"></OrderDetails>
  </div>
</template>

<script>
import OrderDetails from '@/views/order/OrderDetails.vue';
import Config from '@/settings';
import MerchantUserApi from '@/api/MerchantUserApi';

export default {
  name: 'CustomerOrderList',
  props: {
    userId: { type: String, default: '' }
  },
  data() {
    return {
      tableData: [],
      searchForm: {
        userId: ''
      },
      pagination: {
        total: 0,
        size: Config.pageSize,
        current: 1
      },
      loading: false
    };
  },
  components: { OrderDetails },
  mounted() {
    this.searchForm.userId = this.userId;
    this.refreshList();
  },
  methods: {
    toDetail(row) {
      this.$refs.orderDetails.init(row.orderCode);
    },
    search() {
      this.pagination.current = 1;
      this.refreshList();
    },
    reset() {
      this.$refs.searchForm.resetFields();
      this.pagination.current = 1;
      this.refreshList();
    },
    refreshList() {
      this.loading = true;
      MerchantUserApi.merchantCustomerPotentialOrderList(this.pagination, this.searchForm).then((data) => {
        this.tableData = data.records;
        this.pagination.total = data.total;
        // 删除后如果当前页没数据,则返回前一页
        if (data.records.length === 0 && data.total > 0 && this.pagination.current > 1) {
          this.pagination.current = this.pagination.current - 1;
          this.refreshList();
        }
        this.loading = false;
      });
    },
    handlePageSizeChange() {
      this.pagination.current = 1;
      this.refreshList();
    },
    handlePageCurrentChange() {
      this.refreshList();
    }
  }
};
</script>

<style lang="scss" scoped>
</style>