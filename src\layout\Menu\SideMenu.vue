<template>
  <el-menu :default-active="activedPath" :background-color="variables.menuBg" :text-color="variables.menuText" :active-text-color="variables.menuActiveText"
    :collapse-transition="false" router mode="vertical">
    <side-menu-item v-for="route in routeList" :key="route.path" :item="route" />
  </el-menu>
</template>

<script>
import variables from '@/assets/styles/variables.module.scss';
import SideMenuItem from './SideMenuItem.vue';
// import { menuData } from '@/router/menu.js';
import { mapGetters } from 'vuex';

export default {
  name: 'side-menu',
  data() {
    return {};
  },
  components: { SideMenuItem },
  computed: {
    ...mapGetters('tags', ['cachedViews']),
    ...mapGetters('router', ['menuRoutes']),
    variables() {
      return variables;
    },
    routeList() {
      return this.menuRoutes;
      // return menuData;
    },
    activedPath() {
      // const { path } = currentRoute.value;
      // return path;
      return this.$route.path;
    }
  },
  methods: {}
};
</script>

<style lang="scss" scoped>
</style>