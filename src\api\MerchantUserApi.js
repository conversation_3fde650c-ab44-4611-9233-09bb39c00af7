import request from '@/utils/request';

export default {
  merchantCustomerList(pagination, searchForm) {
    return request({
      url: '/merchant/customer/list',
      method: 'post',
      data: {
        ...pagination,
        ...searchForm
      }
    });
  },
  merchantCustomeOrderList(pagination, searchForm) {
    return request({
      url: '/merchant/customer/orderList',
      method: 'post',
      data: {
        ...pagination,
        ...searchForm
      }
    });
  },

  merchantCustomerInfo(userId) {
    return request({
      url: '/merchant/customer/customerInfo',
      method: 'post',
      data: {
        userId
      }
    });
  },
  merchantCustomerPotentialList(pagination, searchForm) {
    return request({
      url: '/merchant/customer/potentialList',
      method: 'post',
      data: {
        ...pagination,
        ...searchForm
      }
    });
  },
  merchantCustomerPotentialOrderList(pagination, searchForm) {
    return request({
      url: '/merchant/customer/potentialOrderList',
      method: 'post',
      data: {
        ...pagination,
        ...searchForm
      }
    });
  },
};
